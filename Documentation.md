# 🖥️ Native RDP Setup on Debian 12 with Chrome Auto-Launch for DAT

This guide provides everything needed to install, configure, and manage a multi-user RDP environment using XFCE, xRDP, and Google Chrome on **Debian 12**. It is designed for secure access to DAT Load Board through native RDP clients, with auto-launch and session cloning features.

---

## ✅ Features

- Lightweight XFCE desktop environment
- Secure xRDP access for multiple users
- Chrome launches automatically to DAT Load Board
- Scripted user management (`add` / `delete`)
- DAT session can be cloned to multiple users
- Enhanced error handling and logging
- SSL/TLS security configuration

---

## 📋 System Requirements

- **OS**: Debian 12 (Bookworm)
- **RAM**: Minimum 2GB (4GB+ recommended for multiple users)
- **Disk**: 10GB+ free space
- **Network**: Port 3389 accessible for RDP connections

---

## ⚙️ 1. System Preparation

Update the system and install essential dependencies:

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential tools
sudo apt install -y wget curl gnupg2 software-properties-common ufw
```

---

## 🧰 2. Install XFCE Desktop & xRDP

Install XFCE desktop environment:

```bash
# Install XFCE desktop
sudo apt install -y xfce4 xfce4-goodies

# Install xRDP server
sudo apt install -y xrdp

# Enable and start xRDP service
sudo systemctl enable xrdp
sudo systemctl start xrdp

# Configure firewall
sudo ufw allow 3389/tcp
sudo ufw --force enable
```

---

## 🖼️ 3. Set XFCE as Default Session

Configure XFCE as the default desktop session:

```bash
# Set XFCE for current user
echo xfce4-session > ~/.xsession

# Set XFCE as default for new users
sudo cp ~/.xsession /etc/skel/.xsession
sudo chmod +x /etc/skel/.xsession
```

---

## 🌐 4. Install Google Chrome

Download and install Google Chrome with dependencies:

```bash
# Download Chrome
wget -q https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb

# Install Chrome and dependencies
sudo apt install -y ./google-chrome-stable_current_amd64.deb

# Clean up
rm google-chrome-stable_current_amd64.deb

# Install additional dependencies for RDP environment
sudo apt install -y fonts-liberation libappindicator3-1 libasound2 libatk-bridge2.0-0 \
  libdrm2 libgtk-3-0 libnspr4 libnss3 libxcomposite1 libxdamage1 libxrandr2 \
  xdg-utils libxss1 libgconf-2-4
```

---

## 🚀 5. Configure Chrome Auto-Launch for New Users

Create auto-start script for Chrome:

```bash
# Create auto-start script for new users
sudo tee /etc/skel/.xsessionrc > /dev/null << 'EOF'
#!/bin/bash

# Wait for desktop to load
sleep 3

# Launch Chrome with DAT Load Board
google-chrome --no-first-run --no-default-browser-check --disable-infobars \
  --disable-extensions --no-sandbox --disable-dev-shm-usage \
  --start-maximized https://one.dat.com/dashboard &
EOF

# Make script executable
sudo chmod +x /etc/skel/.xsessionrc
```

---

## 👥 6. Enhanced User Management Script

Create an improved user management script with error handling:

```bash
sudo tee /usr/local/bin/manage-rdp-user > /dev/null << 'EOF'
#!/bin/bash

# Enhanced RDP User Management Script
# Usage: manage-rdp-user add|delete|list <username>

set -euo pipefail

CMD=$1
USERNAME=${2:-}
LOGFILE="/var/log/rdp-user-management.log"

# Logging function
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | sudo tee -a "$LOGFILE"
}

# Validate input
if [[ -z "$CMD" ]]; then
    echo "Usage: manage-rdp-user add|delete|list [username]"
    exit 1
fi

case "$CMD" in
    "add")
        if [[ -z "$USERNAME" ]]; then
            echo "Error: Username required for add command"
            exit 1
        fi

        if id "$USERNAME" &>/dev/null; then
            echo "❌ User '$USERNAME' already exists."
            exit 1
        fi

        # Generate secure password
        PASSWORD=$(openssl rand -base64 16)

        # Create user
        useradd -m -s /bin/bash "$USERNAME"
        echo "$USERNAME:$PASSWORD" | chpasswd

        # Set up Chrome auto-launch
        cp /etc/skel/.xsessionrc /home/<USER>/.xsessionrc
        cp /etc/skel/.xsession /home/<USER>/.xsession
        chown $USERNAME:$USERNAME /home/<USER>/.xsessionrc
        chown $USERNAME:$USERNAME /home/<USER>/.xsession

        # Create Chrome config directory
        mkdir -p /home/<USER>/.config/google-chrome
        chown -R $USERNAME:$USERNAME /home/<USER>/.config

        log_message "User '$USERNAME' created successfully"
        echo "✅ User '$USERNAME' created successfully."
        echo "🔐 Temporary Password: $PASSWORD"
        echo "📝 Please save this password - it won't be shown again!"
        ;;

    "delete")
        if [[ -z "$USERNAME" ]]; then
            echo "Error: Username required for delete command"
            exit 1
        fi

        if ! id "$USERNAME" &>/dev/null; then
            echo "❌ User '$USERNAME' does not exist."
            exit 1
        fi

        # Kill user processes
        pkill -KILL -u "$USERNAME" 2>/dev/null || true

        # Remove user and home directory
        userdel -r "$USERNAME"

        log_message "User '$USERNAME' deleted successfully"
        echo "🗑️ User '$USERNAME' deleted successfully."
        ;;

    "list")
        echo "📋 RDP Users:"
        getent passwd | grep '/home/' | cut -d: -f1 | sort
        ;;

    *)
        echo "Usage: manage-rdp-user add|delete|list [username]"
        exit 1
        ;;
esac
EOF

# Make script executable
sudo chmod +x /usr/local/bin/manage-rdp-user

# Create log file
sudo touch /var/log/rdp-user-management.log
sudo chmod 644 /var/log/rdp-user-management.log
```

### User Management Examples:

```bash
# Add a new user
sudo manage-rdp-user add john

# Delete a user
sudo manage-rdp-user delete john

# List all RDP users
sudo manage-rdp-user list
```

---

## 🔁 7. Clone DAT Session Across Users (Optional)

To share a logged-in DAT session across multiple users:

```bash
# 1. First, log in as user1 via RDP and authenticate with DAT in Chrome
# 2. Then clone the session data to other users:

# Example: Clone user1's session to user2, user3, user4
for u in user2 user3 user4; do
    # Ensure user is logged out
    sudo pkill -KILL -u "$u" 2>/dev/null || true

    # Copy Chrome profile data
    sudo rsync -a /home/<USER>/.config/google-chrome/ /home/<USER>/.config/google-chrome/

    # Fix ownership
    sudo chown -R $u:$u /home/<USER>/.config/google-chrome

    echo "✅ Cloned session to user: $u"
done
```

---

## 🔒 8. Security Enhancements (Recommended)

### Configure SSL/TLS for xRDP:

```bash
# Generate SSL certificate
sudo openssl req -x509 -newkey rsa:2048 -nodes -keyout /etc/xrdp/key.pem \
  -out /etc/xrdp/cert.pem -days 365 -subj "/C=US/ST=State/L=City/O=Org/CN=localhost"

# Set permissions
sudo chown xrdp:xrdp /etc/xrdp/key.pem /etc/xrdp/cert.pem
sudo chmod 400 /etc/xrdp/key.pem
sudo chmod 444 /etc/xrdp/cert.pem

# Configure xRDP to use SSL
sudo sed -i 's/certificate=/certificate=\/etc\/xrdp\/cert.pem/' /etc/xrdp/xrdp.ini
sudo sed -i 's/key_file=/key_file=\/etc\/xrdp\/key.pem/' /etc/xrdp/xrdp.ini

# Restart xRDP
sudo systemctl restart xrdp
```

### Configure session timeouts:

```bash
# Add session timeout to xRDP config
sudo tee -a /etc/xrdp/xrdp.ini > /dev/null << 'EOF'

# Session timeout (in seconds) - 8 hours
max_idle_time=28800
max_disc_time=28800
EOF

sudo systemctl restart xrdp
```

---

## 🧪 9. Testing & Verification

### Test RDP Connection:

1. **From Windows**: Open "Remote Desktop Connection"
2. **From macOS**: Use "Microsoft Remote Desktop" app
3. **From Linux**: Use `rdesktop` or `xfreerdp`

**Connection Details:**
- **Server**: `your-server-ip:3389`
- **Username**: Created user (e.g., `john`)
- **Password**: Generated password from user creation

### Verification Checklist:

- [ ] RDP connection establishes successfully
- [ ] XFCE desktop loads properly
- [ ] Chrome launches automatically
- [ ] DAT Load Board opens in Chrome
- [ ] Multiple users can connect simultaneously
- [ ] Session cloning works (if configured)

### Troubleshooting Commands:

```bash
# Check xRDP status
sudo systemctl status xrdp

# Check xRDP logs
sudo tail -f /var/log/xrdp.log

# Check user management logs
sudo tail -f /var/log/rdp-user-management.log

# Test Chrome launch manually
google-chrome --no-sandbox --disable-dev-shm-usage https://one.dat.com/dashboard

# Check active RDP sessions
who
```

---

## 📚 Additional Resources

- [xRDP Documentation](http://xrdp.org/)
- [XFCE Documentation](https://docs.xfce.org/)
- [DAT Load Board](https://one.dat.com/dashboard)

---

## 🆘 Support

If you encounter issues:

1. Check the logs: `/var/log/xrdp.log` and `/var/log/rdp-user-management.log`
2. Verify firewall settings: `sudo ufw status`
3. Test Chrome manually: `google-chrome --version`
4. Check user permissions: `ls -la /home/<USER>/`

---

*Last updated: $(date '+%Y-%m-%d')*