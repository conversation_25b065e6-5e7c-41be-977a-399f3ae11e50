# 🖥️ Native RDP Setup on Debian 12 with Chrome Auto-Launch for DAT

This guide provides everything needed to install, configure, and manage a multi-user RDP environment using XFCE, xRDP, and Google Chrome on **Debian 12**. It is designed for secure access to DAT Load Board through native RDP clients, with auto-launch and session cloning features.

---

## ✅ Features

- Lightweight XFCE desktop
- Secure xRDP access for multiple users
- Chrome launches automatically to DAT
- Scripted user management (`add` / `delete`)
- DAT session can be cloned to multiple users

---

## ⚙️ 1. System Preparation

Update the system:

```bash
sudo apt update && sudo apt upgrade -y

🧰 2. Install XFCE Desktop & xRDP
sudo apt install -y xfce4 xfce4-goodies
sudo apt install -y xrdp
sudo systemctl enable xrdp
sudo systemctl start xrdp
sudo ufw allow 3389/tcp

🖼️ 3. Set XFCE as Default Session
echo xfce4-session > ~/.xsession
sudo cp ~/.xsession /etc/skel/.xsession

🌐 4. Install Google Chrome
wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
sudo apt install -y ./google-chrome-stable_current_amd64.deb
rm google-chrome-stable_current_amd64.deb

🚀 5. Configure Chrome Auto-Launch for New Users

Create auto-start script:
sudo tee /etc/skel/.xsessionrc > /dev/null << 'EOF'
#!/bin/bash
google-chrome --no-first-run https://www.dat.com/load-board &
EOF

sudo chmod +x /etc/skel/.xsessionrc

👥 6. User Management Script

Create manage-rdp-user:
sudo tee /usr/local/bin/manage-rdp-user > /dev/null << 'EOF'
#!/bin/bash

CMD=$1
USERNAME=$2

if [[ "$CMD" == "add" ]]; then
  if id "$USERNAME" &>/dev/null; then
    echo "User '$USERNAME' already exists."
    exit 1
  fi

  PASSWORD=$(openssl rand -base64 12)
  useradd -m -s /bin/bash "$USERNAME"
  echo "$USERNAME:$PASSWORD" | chpasswd

  cp /etc/skel/.xsessionrc /home/<USER>/.xsessionrc
  chown $USERNAME:$USERNAME /home/<USER>/.xsessionrc

  echo "✅ User '$USERNAME' created."
  echo "🔐 Temporary Password: $PASSWORD"
elif [[ "$CMD" == "delete" ]]; then
  if ! id "$USERNAME" &>/dev/null; then
    echo "User '$USERNAME' does not exist."
    exit 1
  fi
  pkill -KILL -u "$USERNAME"
  userdel -r "$USERNAME"
  echo "🗑️ User '$USERNAME' deleted."
else
  echo "Usage: manage-rdp-user add|delete <username>"
fi
EOF

sudo chmod +x /usr/local/bin/manage-rdp-user

