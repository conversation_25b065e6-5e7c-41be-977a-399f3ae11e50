const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class MasterSessionManager {
    constructor() {
        this.masterBrowser = null;
        this.masterPage = null;
        this.isAuthenticated = false;
        this.sessionData = null;
        this.keepAliveInterval = null;
        this.sessionClones = new Map();
    }

    async initializeMasterBrowser() {
        console.log('🚀 Initializing Master DAT Browser...');
        
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-gpu',
                '--disable-extensions',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        console.log('✅ Master browser initialized');
        return this.masterPage;
    }

    async authenticateMaster(email, password) {
        console.log('🔐 Authenticating Master Browser...');
        
        if (!this.masterPage) {
            await this.initializeMasterBrowser();
        }

        try {
            // Navigate to DAT dashboard (will redirect to login)
            console.log('📍 Navigating to DAT dashboard...');
            await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'networkidle2' });
            
            // Enter email
            await this.masterPage.waitForSelector('input[type="email"], input[name="username"], input[name="email"]', { timeout: 10000 });
            const emailField = await this.masterPage.$('input[type="email"], input[name="username"], input[name="email"]');
            await emailField.click();
            await emailField.type(email, { delay: 100 });
            console.log('✅ Email entered');
            
            // Click Continue
            const continueButton = await this.masterPage.$('button[type="submit"]');
            await continueButton.click();
            await new Promise(resolve => setTimeout(resolve, 3000));
            console.log('✅ Continue clicked');
            
            // Enter password
            await this.masterPage.waitForSelector('input[type="password"]', { timeout: 10000 });
            const passwordField = await this.masterPage.$('input[type="password"]');
            await passwordField.click();
            await passwordField.type(password, { delay: 100 });
            console.log('✅ Password entered');
            
            // Click Login
            const loginButton = await this.masterPage.$('button[type="submit"]');
            await loginButton.click();
            await new Promise(resolve => setTimeout(resolve, 5000));
            console.log('✅ Login clicked');
            
            // Handle 2FA interactively
            console.log('🔐 Checking for 2FA...');
            await this.handle2FAInteractive();
            
            // Verify authentication
            await this.verifyAuthentication();
            
            if (this.isAuthenticated) {
                console.log('🎉 Master browser authenticated successfully!');
                await this.startKeepAlive();
                await this.startLiveBrowserServer();
                return true;
            } else {
                throw new Error('Authentication verification failed');
            }
            
        } catch (error) {
            console.error('❌ Master authentication failed:', error.message);
            return false;
        }
    }

    async handle2FAInteractive() {
        try {
            // Look for "Try another method"
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const buttons = await this.masterPage.$$('button, a, span');
            for (const button of buttons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('try another method')) {
                    await button.click();
                    console.log('✅ Clicked "Try another method"');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            
            // Select email option
            const emailButtons = await this.masterPage.$$('button, a, div, span');
            for (const button of emailButtons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('email') && !text.toLowerCase().includes('phone')) {
                    await button.click();
                    console.log('✅ Selected email verification');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    break;
                }
            }
            
            // Wait for code input and prompt user
            await this.masterPage.waitForSelector('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]', { timeout: 15000 });
            
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            console.log('\n📧 EMAIL VERIFICATION CODE NEEDED FOR MASTER SESSION!');
            const emailCode = await new Promise(resolve => {
                rl.question('🔐 Enter the 6-digit verification code: ', resolve);
            });
            rl.close();
            
            // Enter code
            const codeField = await this.masterPage.$('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]');
            await codeField.click();
            await codeField.type(emailCode.trim(), { delay: 100 });
            
            // Click verify
            const verifyButton = await this.masterPage.$('button[type="submit"]');
            await verifyButton.click();
            console.log('✅ 2FA verify button clicked, waiting for authentication...');

            // Wait longer for 2FA to complete and page to navigate
            await new Promise(resolve => setTimeout(resolve, 15000));

            console.log('✅ 2FA completed');

            // Handle popup sequence properly
            await this.handlePopupSequence();
            
        } catch (error) {
            console.log('⚠️ 2FA handling error:', error.message);
        }
    }

    async handlePopupSequence() {
        console.log('🔄 Starting proper popup sequence handling...');

        try {
            // Step 1: Wait 5 seconds after login to see what popups appear
            console.log('⏳ Step 1: Waiting 5 seconds to see what popups appear...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot to see initial popup state
            await this.masterPage.screenshot({ path: 'popup-sequence-step1-initial.png', fullPage: true });
            console.log('📸 Step 1 screenshot saved: popup-sequence-step1-initial.png');

            // Step 2: Check if we're still on the right page before closing popups
            let currentUrl = this.masterPage.url();
            console.log(`📍 Current URL after 2FA: ${currentUrl}`);

            if (currentUrl.includes('login')) {
                console.log('❌ Still on login page after 2FA - authentication may have failed');
                return;
            }

            // Step 2: Interactive popup handling
            console.log('🔍 Step 2: Interactive popup analysis and handling...');
            await this.interactivePopupHandling();

            // Take screenshot after closing other popups
            await this.masterPage.screenshot({ path: 'popup-sequence-step2-after-close.png', fullPage: true });
            console.log('📸 Step 2 screenshot saved: popup-sequence-step2-after-close.png');

            // Step 3: Wait another 5 seconds for page to settle
            console.log('⏳ Step 3: Waiting 5 seconds for page to settle...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot to see settled state
            await this.masterPage.screenshot({ path: 'popup-sequence-step3-settled.png', fullPage: true });
            console.log('📸 Step 3 screenshot saved: popup-sequence-step3-settled.png');

            // Step 4: Now click "Login anyway" if it's at the top
            console.log('🔐 Step 4: Looking for "Login anyway" button at the top...');
            await this.clickLoginAnywayIfVisible();

            // Take final screenshot
            await this.masterPage.screenshot({ path: 'popup-sequence-step4-final.png', fullPage: true });
            console.log('📸 Step 4 screenshot saved: popup-sequence-step4-final.png');

        } catch (error) {
            console.log('⚠️ Popup sequence error:', error.message);

            // Take error screenshot
            await this.masterPage.screenshot({ path: 'popup-sequence-error.png', fullPage: true });
            console.log('📸 Error screenshot saved: popup-sequence-error.png');
        }
    }

    async interactivePopupHandling() {
        const readline = require('readline');
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        try {
            console.log('\n🔍 INTERACTIVE POPUP ANALYSIS');
            console.log('=====================================');

            // Analyze all visible popups and elements
            const popupAnalysis = await this.analyzeVisiblePopups();

            // Take screenshot for user to see
            await this.masterPage.screenshot({ path: 'interactive-popup-analysis.png', fullPage: true });
            console.log('📸 Current state screenshot saved: interactive-popup-analysis.png');

            console.log(`\n📊 Found ${popupAnalysis.length} potential popups/modals/dialogs:`);

            if (popupAnalysis.length === 0) {
                console.log('✅ No popups detected - proceeding to next step');
                return;
            }

            // Show each popup to user
            for (let i = 0; i < popupAnalysis.length; i++) {
                const popup = popupAnalysis[i];

                console.log(`\n🔍 POPUP ${i + 1} of ${popupAnalysis.length}:`);
                console.log(`📍 Location: (${popup.top}, ${popup.left})`);
                console.log(`📏 Size: ${popup.width} x ${popup.height}`);
                console.log(`🏷️ Element: ${popup.tagName} - class:"${popup.className}" - id:"${popup.id}"`);
                console.log(`📝 Text Content: "${popup.textContent}"`);
                console.log(`🎯 Z-Index: ${popup.zIndex} (numeric: ${popup.zIndexNumeric})`);
                console.log(`📐 Position: ${popup.position}`);
                console.log(`👁️ Visibility: ${popup.visibility}, Display: ${popup.display}, Opacity: ${popup.opacity}`);

                if (popup.buttons.length > 0) {
                    console.log(`🔘 Available Buttons:`);
                    popup.buttons.forEach((btn, btnIndex) => {
                        console.log(`   ${btnIndex + 1}. "${btn.text}" - ${btn.tagName} (${btn.width}x${btn.height})`);
                    });
                }

                console.log(`\n🎮 What would you like to do with this popup?`);
                console.log(`1. Close it (try to find close button)`);
                console.log(`2. Click a specific button (I'll show you options)`);
                console.log(`3. Press Escape key`);
                console.log(`4. Skip this popup`);
                console.log(`5. Take another screenshot to see current state`);

                const action = await new Promise(resolve => {
                    rl.question('👉 Enter your choice (1-5): ', resolve);
                });

                switch (action.trim()) {
                    case '1':
                        console.log('🚫 Attempting to close popup...');
                        await this.closeSpecificPopup(popup);
                        break;

                    case '2':
                        if (popup.buttons.length > 0) {
                            console.log('\n🔘 Available buttons:');
                            popup.buttons.forEach((btn, btnIndex) => {
                                console.log(`   ${btnIndex + 1}. "${btn.text}"`);
                            });

                            const btnChoice = await new Promise(resolve => {
                                rl.question('👉 Which button to click (number): ', resolve);
                            });

                            const btnIndex = parseInt(btnChoice.trim()) - 1;
                            if (btnIndex >= 0 && btnIndex < popup.buttons.length) {
                                console.log(`🔘 Clicking button: "${popup.buttons[btnIndex].text}"`);
                                await this.clickSpecificButton(popup.buttons[btnIndex]);
                            } else {
                                console.log('❌ Invalid button choice');
                            }
                        } else {
                            console.log('❌ No buttons found in this popup');
                        }
                        break;

                    case '3':
                        console.log('⌨️ Pressing Escape key...');
                        await this.masterPage.keyboard.press('Escape');
                        break;

                    case '4':
                        console.log('⏭️ Skipping this popup');
                        continue;

                    case '5':
                        await this.masterPage.screenshot({ path: `interactive-popup-update-${i + 1}.png`, fullPage: true });
                        console.log(`📸 Updated screenshot saved: interactive-popup-update-${i + 1}.png`);
                        i--; // Repeat this popup
                        continue;

                    default:
                        console.log('❌ Invalid choice, skipping popup');
                        continue;
                }

                // Wait after action
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Take screenshot after action
                await this.masterPage.screenshot({ path: `interactive-after-action-${i + 1}.png`, fullPage: true });
                console.log(`📸 After action screenshot saved: interactive-after-action-${i + 1}.png`);

                // Ask for confirmation
                const confirm = await new Promise(resolve => {
                    rl.question('✅ Was the action successful? (y/n): ', resolve);
                });

                if (confirm.toLowerCase() !== 'y') {
                    console.log('🔄 Action was not successful. Let\'s try a different approach...');

                    console.log('\n🛠️ Alternative methods:');
                    console.log('1. Try clicking at specific coordinates');
                    console.log('2. Try different selector approach');
                    console.log('3. Try JavaScript click');
                    console.log('4. Continue anyway');

                    const altAction = await new Promise(resolve => {
                        rl.question('👉 Choose alternative method (1-4): ', resolve);
                    });

                    switch (altAction.trim()) {
                        case '1':
                            const coords = await new Promise(resolve => {
                                rl.question('👉 Enter coordinates (x,y): ', resolve);
                            });
                            const [x, y] = coords.split(',').map(n => parseInt(n.trim()));
                            if (!isNaN(x) && !isNaN(y)) {
                                console.log(`🎯 Clicking at coordinates (${x}, ${y})`);
                                await this.masterPage.mouse.click(x, y);
                            }
                            break;

                        case '2':
                            const selector = await new Promise(resolve => {
                                rl.question('👉 Enter CSS selector to click: ', resolve);
                            });
                            try {
                                await this.masterPage.click(selector);
                                console.log(`✅ Clicked selector: ${selector}`);
                            } catch (e) {
                                console.log(`❌ Failed to click selector: ${e.message}`);
                            }
                            break;

                        case '3':
                            console.log('🔧 Trying JavaScript click on popup element...');
                            await this.masterPage.evaluate((popupData) => {
                                const elements = document.querySelectorAll('*');
                                for (const el of elements) {
                                    const rect = el.getBoundingClientRect();
                                    if (Math.abs(rect.top - popupData.top) < 5 &&
                                        Math.abs(rect.left - popupData.left) < 5) {
                                        el.click();
                                        break;
                                    }
                                }
                            }, popup);
                            break;

                        case '4':
                            console.log('⏭️ Continuing anyway...');
                            break;
                    }

                    // Take screenshot after alternative action
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    await this.masterPage.screenshot({ path: `interactive-alt-action-${i + 1}.png`, fullPage: true });
                    console.log(`📸 After alternative action screenshot saved`);
                }
            }

            // Final confirmation
            await this.masterPage.screenshot({ path: 'interactive-final-state.png', fullPage: true });
            console.log('📸 Final state screenshot saved: interactive-final-state.png');

            const finalConfirm = await new Promise(resolve => {
                rl.question('\n✅ Are all popups handled? Ready to proceed? (y/n): ', resolve);
            });

            if (finalConfirm.toLowerCase() !== 'y') {
                console.log('🔄 Let\'s analyze popups again...');
                await this.interactivePopupHandling(); // Recursive call
            } else {
                console.log('🎉 Popup handling complete!');
            }

        } finally {
            rl.close();
        }
    }

    async analyzeVisiblePopups() {
        console.log('🔍 Analyzing ALL visible elements (comprehensive scan)...');

        const popupAnalysis = await this.masterPage.evaluate(() => {
            const popups = [];
            const allElements = document.querySelectorAll('*');

            console.log(`Scanning ${allElements.length} elements...`);

            for (const el of allElements) {
                const rect = el.getBoundingClientRect();
                const style = window.getComputedStyle(el);

                // Much more inclusive criteria - capture ANY element that could be a popup
                if (rect.width > 50 && rect.height > 50 &&
                    style.visibility !== 'hidden' &&
                    style.display !== 'none' &&
                    (
                        // Any positioned element
                        (style.position === 'fixed' || style.position === 'absolute') ||
                        // Any element with modal/popup/dialog keywords
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('modal')) ||
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('popup')) ||
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('dialog')) ||
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('overlay')) ||
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('toast')) ||
                        (el.className && el.className.toLowerCase && el.className.toLowerCase().includes('notification')) ||
                        // Any element with dialog roles
                        el.getAttribute('role') === 'dialog' ||
                        el.getAttribute('role') === 'alertdialog' ||
                        el.getAttribute('role') === 'banner' ||
                        // Any element with Z-index (even low ones)
                        (style.zIndex !== 'auto' && parseInt(style.zIndex) > 0) ||
                        // Any element containing specific text patterns
                        (el.textContent && (
                            el.textContent.toLowerCase().includes('post your trucks') ||
                            el.textContent.toLowerCase().includes('click here to post') ||
                            el.textContent.toLowerCase().includes('login anyway') ||
                            el.textContent.toLowerCase().includes('close') ||
                            el.textContent.toLowerCase().includes('×') ||
                            el.textContent.toLowerCase().includes('✕')
                        ))
                    )
                ) {
                    // Find buttons within this popup
                    const buttons = [];
                    const buttonElements = el.querySelectorAll('button, a[role="button"], [role="button"], input[type="button"], input[type="submit"]');

                    for (const btn of buttonElements) {
                        const btnRect = btn.getBoundingClientRect();
                        const btnStyle = window.getComputedStyle(btn);

                        if (btnRect.width > 0 && btnRect.height > 0 &&
                            btnStyle.visibility !== 'hidden' &&
                            btnStyle.display !== 'none') {
                            buttons.push({
                                text: (btn.textContent || btn.value || btn.getAttribute('aria-label') || '').trim(),
                                tagName: btn.tagName,
                                className: btn.className,
                                id: btn.id,
                                top: btnRect.top,
                                left: btnRect.left,
                                width: btnRect.width,
                                height: btnRect.height
                            });
                        }
                    }

                    popups.push({
                        tagName: el.tagName,
                        className: el.className || '',
                        id: el.id || '',
                        textContent: (el.textContent || '').trim().substring(0, 300),
                        innerHTML: (el.innerHTML || '').substring(0, 500),
                        top: Math.round(rect.top),
                        left: Math.round(rect.left),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height),
                        zIndex: style.zIndex || 'auto',
                        zIndexNumeric: parseInt(style.zIndex) || 0,
                        position: style.position || 'static',
                        opacity: style.opacity || '1',
                        visibility: style.visibility || 'visible',
                        display: style.display || 'block',
                        buttons: buttons
                    });
                }
            }

            // Sort by z-index (highest first), then by position (fixed/absolute first), then by size
            return popups.sort((a, b) => {
                // First priority: Z-index (highest first)
                if (a.zIndexNumeric !== b.zIndexNumeric) {
                    return b.zIndexNumeric - a.zIndexNumeric;
                }

                // Second priority: Position type (fixed/absolute first)
                const aPositioned = (a.position === 'fixed' || a.position === 'absolute') ? 1 : 0;
                const bPositioned = (b.position === 'fixed' || b.position === 'absolute') ? 1 : 0;
                if (aPositioned !== bPositioned) {
                    return bPositioned - aPositioned;
                }

                // Third priority: Size (smaller popups first, as they're likely on top)
                return (a.width * a.height) - (b.width * b.height);
            });
        });

        return popupAnalysis;
    }

    async closeSpecificPopup(popup) {
        console.log(`🚫 Attempting to close popup: ${popup.tagName}`);

        try {
            // Try to find close button within the popup
            const closeButton = await this.masterPage.evaluate((popupData) => {
                const allElements = document.querySelectorAll('*');

                // Find the popup element first
                let popupElement = null;
                for (const el of allElements) {
                    const rect = el.getBoundingClientRect();
                    if (Math.abs(rect.top - popupData.top) < 5 &&
                        Math.abs(rect.left - popupData.left) < 5 &&
                        Math.abs(rect.width - popupData.width) < 5 &&
                        Math.abs(rect.height - popupData.height) < 5) {
                        popupElement = el;
                        break;
                    }
                }

                if (!popupElement) return null;

                // Look for close buttons within this popup
                const closeSelectors = [
                    'button[aria-label*="close" i]',
                    'button[title*="close" i]',
                    'button[aria-label*="dismiss" i]',
                    '.close-button',
                    '.modal-close',
                    '.popup-close'
                ];

                for (const selector of closeSelectors) {
                    const closeBtn = popupElement.querySelector(selector);
                    if (closeBtn) {
                        closeBtn.click();
                        return true;
                    }
                }

                // Look for X symbols
                const allButtons = popupElement.querySelectorAll('button, span, div, a');
                for (const btn of allButtons) {
                    const text = btn.textContent.trim();
                    if (text === '×' || text === '✕' || text === 'X') {
                        btn.click();
                        return true;
                    }
                }

                return false;
            }, popup);

            if (closeButton) {
                console.log('✅ Found and clicked close button');
            } else {
                console.log('⚠️ No close button found, trying Escape key');
                await this.masterPage.keyboard.press('Escape');
            }

        } catch (error) {
            console.log(`❌ Error closing popup: ${error.message}`);
        }
    }

    async clickSpecificButton(button) {
        console.log(`🔘 Clicking button: "${button.text}"`);

        try {
            await this.masterPage.evaluate((btnData) => {
                const allButtons = document.querySelectorAll('button, a, [role="button"], input[type="button"], input[type="submit"]');

                for (const btn of allButtons) {
                    const rect = btn.getBoundingClientRect();
                    if (Math.abs(rect.top - btnData.top) < 5 &&
                        Math.abs(rect.left - btnData.left) < 5) {
                        btn.click();
                        return true;
                    }
                }
                return false;
            }, button);

            console.log('✅ Button clicked');

        } catch (error) {
            console.log(`❌ Error clicking button: ${error.message}`);
        }
    }

    async closeAllPopupsExceptLoginAnyway() {
        console.log('🚫 Closing all blocking popups except "Login anyway"...');

        // Multiple rounds to handle layered popups
        for (let round = 0; round < 5; round++) {
            console.log(`🔄 Popup closing round ${round + 1}...`);

            let foundPopup = false;

            // First, look for obvious close buttons (X, Close, etc.) - be more aggressive
            const closeSelectors = [
                // Standard close button selectors
                'button[aria-label="Close"]',
                'button[title="Close"]',
                'button[aria-label="close"]',
                'button[title="close"]',
                '.close-button',
                '.modal-close',
                '.popup-close',
                '.dialog-close',
                '[data-testid*="close"]',
                '[data-testid*="dismiss"]',

                // X symbols in various elements
                'button:contains("×")',
                'button:contains("✕")',
                'span:contains("×")',
                'span:contains("✕")',
                'div:contains("×")',
                'div:contains("✕")',
                'a:contains("×")',
                'a:contains("✕")',

                // Common modal/popup patterns
                '.modal button',
                '.popup button',
                '.dialog button',
                '.overlay button',
                '[role="dialog"] button',
                '[role="alertdialog"] button',

                // Icon-based close buttons
                '.fa-times',
                '.fa-close',
                '.fa-x',
                '.icon-close',
                '.icon-times',
                '.icon-x',

                // Generic selectors for any clickable element with close-like appearance
                '*[class*="close"]',
                '*[id*="close"]',
                '*[class*="dismiss"]',
                '*[id*="dismiss"]'
            ];

            for (const selector of closeSelectors) {
                try {
                    const closeButtons = await this.masterPage.$$(selector);

                    for (const closeButton of closeButtons) {
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none' &&
                                   style.opacity !== '0';
                        }, closeButton);

                        if (isVisible) {
                            console.log(`🚫 Found close button with selector: ${selector}`);

                            try {
                                await closeButton.click();
                                console.log(`✅ Clicked close button`);
                                foundPopup = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), closeButton);
                                    console.log(`✅ Clicked close button (alternative)`);
                                    foundPopup = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    }

                    if (foundPopup) break;

                } catch (e) {
                    // Continue to next selector
                }
            }

            // If no obvious close buttons, try clicking elements in top-right corners (where close buttons usually are)
            if (!foundPopup) {
                console.log('🔍 Looking for close buttons in top-right corners...');

                const allClickableElements = await this.masterPage.$$('button, a, span, div, i, svg, [role="button"]');

                for (const element of allClickableElements) {
                    try {
                        const elementInfo = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);

                            return {
                                isVisible: rect.width > 0 && rect.height > 0 &&
                                          style.visibility !== 'hidden' &&
                                          style.display !== 'none' &&
                                          style.opacity !== '0',
                                top: rect.top,
                                left: rect.left,
                                right: rect.right,
                                width: rect.width,
                                height: rect.height,
                                className: el.className,
                                id: el.id,
                                tagName: el.tagName,
                                textContent: (el.textContent || '').trim()
                            };
                        }, element);

                        // Look for small clickable elements in the top area that might be close buttons
                        if (elementInfo.isVisible &&
                            elementInfo.top < 600 && // In upper part of screen
                            elementInfo.width < 50 && // Small width (typical for close buttons)
                            elementInfo.height < 50 && // Small height
                            (
                                elementInfo.textContent === '×' ||
                                elementInfo.textContent === '✕' ||
                                elementInfo.textContent === '' || // Empty (might be icon)
                                elementInfo.className.toLowerCase().includes('close') ||
                                elementInfo.className.toLowerCase().includes('dismiss') ||
                                elementInfo.id.toLowerCase().includes('close') ||
                                elementInfo.id.toLowerCase().includes('dismiss')
                            )
                        ) {
                            console.log(`🎯 Found potential close button: ${elementInfo.tagName} at (${elementInfo.top}, ${elementInfo.left}) - "${elementInfo.textContent}" class="${elementInfo.className}"`);

                            try {
                                await element.click();
                                console.log(`✅ Clicked potential close button`);
                                foundPopup = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), element);
                                    console.log(`✅ Clicked potential close button (alternative)`);
                                    foundPopup = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    } catch (e) {
                        // Continue to next element
                    }
                }
            }

            // If still no popup found, look for dismissible elements by text
            if (!foundPopup) {
                const allElements = await this.masterPage.$$('button, a, div, span, [role="button"], [role="dialog"]');

                for (const element of allElements) {
                    try {
                        const text = await this.masterPage.evaluate(el => el.textContent || el.innerText || el.title || el.getAttribute('aria-label'), element);

                        if (text) {
                            const lowerText = text.toLowerCase().trim();

                            // Skip "Login anyway" button - we'll handle it later
                            if (lowerText.includes('login anyway') || lowerText.includes('log in anyway')) {
                                console.log(`⏭️ Skipping "Login anyway" button: "${text.substring(0, 50)}..."`);
                                continue;
                            }

                            // Look for dismissible content
                            if (
                                lowerText === '×' ||
                                lowerText === '✕' ||
                                lowerText.includes('close') ||
                                lowerText.includes('dismiss') ||
                                lowerText.includes('no thanks') ||
                                lowerText.includes('skip') ||
                                lowerText.includes('cancel') ||
                                lowerText.includes('not now') ||
                                lowerText.includes('maybe later') ||
                                lowerText.includes('post your trucks') ||
                                lowerText.includes('click here to post') ||
                                lowerText.includes('get started') ||
                                lowerText.includes('learn more') ||
                                lowerText.includes('got it') ||
                                lowerText.includes('ok') ||
                                lowerText.includes('continue') ||
                                lowerText.includes('next time')
                            ) {
                                // Check if element is visible and clickable
                                const isVisible = await this.masterPage.evaluate(el => {
                                    const rect = el.getBoundingClientRect();
                                    const style = window.getComputedStyle(el);
                                    return rect.width > 0 && rect.height > 0 &&
                                           style.visibility !== 'hidden' &&
                                           style.display !== 'none' &&
                                           style.opacity !== '0';
                                }, element);

                                if (isVisible) {
                                    console.log(`🚫 Closing popup/element: "${text.substring(0, 50)}..."`);

                                    try {
                                        await element.click();
                                        console.log(`✅ Clicked dismissible element`);
                                        foundPopup = true;
                                        await new Promise(resolve => setTimeout(resolve, 2000));
                                        break;
                                    } catch (e) {
                                        try {
                                            await this.masterPage.evaluate(el => el.click(), element);
                                            console.log(`✅ Clicked dismissible element (alternative)`);
                                            foundPopup = true;
                                            await new Promise(resolve => setTimeout(resolve, 2000));
                                            break;
                                        } catch (e2) {
                                            // Continue
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        // Continue to next element
                    }
                }
            }

            // Nuclear option: try pressing Escape key to close modals
            if (!foundPopup && round === 0) {
                console.log('💥 Nuclear option: Pressing Escape key to close modals...');
                try {
                    await this.masterPage.keyboard.press('Escape');
                    console.log('✅ Pressed Escape key');
                    foundPopup = true;
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } catch (e) {
                    console.log('⚠️ Failed to press Escape key');
                }
            }

            // Add detailed debugging for this round
            if (!foundPopup) {
                console.log('🔍 No popup found, analyzing page elements...');

                const pageAnalysis = await this.masterPage.evaluate(() => {
                    const elements = [];
                    const allNodes = document.querySelectorAll('*');

                    for (let i = 0; i < Math.min(allNodes.length, 100); i++) { // Limit to first 100 for performance
                        const el = allNodes[i];
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);

                        if (rect.width > 0 && rect.height > 0 &&
                            style.visibility !== 'hidden' &&
                            style.display !== 'none' &&
                            (
                                (el.textContent && (el.textContent.includes('×') || el.textContent.includes('✕') || el.textContent.toLowerCase().includes('close'))) ||
                                el.className.toLowerCase().includes('close') ||
                                el.className.toLowerCase().includes('modal') ||
                                el.className.toLowerCase().includes('popup') ||
                                (style.position === 'fixed' && parseInt(style.zIndex) > 100)
                            )
                        ) {
                            elements.push({
                                tagName: el.tagName,
                                className: el.className,
                                id: el.id,
                                textContent: (el.textContent || '').trim().substring(0, 50),
                                top: rect.top,
                                left: rect.left,
                                width: rect.width,
                                height: rect.height,
                                zIndex: style.zIndex,
                                position: style.position
                            });
                        }
                    }

                    return elements;
                });

                console.log(`🔍 Found ${pageAnalysis.length} potentially relevant elements:`);
                pageAnalysis.forEach((el, index) => {
                    console.log(`   ${index + 1}. ${el.tagName} - "${el.textContent}" - class:"${el.className}" - pos:(${el.top},${el.left}) - z:${el.zIndex}`);
                });
            }

            // Take screenshot after each round
            await this.masterPage.screenshot({ path: `popup-close-round-${round + 1}.png`, fullPage: true });
            console.log(`📸 Popup close round ${round + 1} screenshot saved`);

            // If no popups found in this round, we're done
            if (!foundPopup) {
                console.log(`ℹ️ No more popups found in round ${round + 1}`);
                break;
            }
        }
    }

    async clickLoginAnywayIfVisible() {
        console.log('🔍 Looking for visible "Login anyway" button...');

        const allElements = await this.masterPage.$$('button, a, div[role="button"], span[role="button"]');

        for (const element of allElements) {
            try {
                const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, element);

                if (text && (
                    text.toLowerCase().includes('login anyway') ||
                    text.toLowerCase().includes('log in anyway')
                )) {
                    // Check if element is visible and at the top (not blocked)
                    const elementInfo = await this.masterPage.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);

                        return {
                            isVisible: rect.width > 0 && rect.height > 0 &&
                                      style.visibility !== 'hidden' &&
                                      style.display !== 'none' &&
                                      style.opacity !== '0',
                            top: rect.top,
                            left: rect.left,
                            zIndex: style.zIndex
                        };
                    }, element);

                    if (elementInfo.isVisible) {
                        console.log(`✅ Found visible "Login anyway" button: "${text}"`);
                        console.log(`📍 Position: top=${elementInfo.top}, left=${elementInfo.left}, zIndex=${elementInfo.zIndex}`);

                        try {
                            await element.click();
                            console.log('✅ Successfully clicked "Login anyway" button');

                            // Wait for navigation
                            await new Promise(resolve => setTimeout(resolve, 5000));

                            return true;

                        } catch (e) {
                            // Try alternative click method
                            try {
                                await this.masterPage.evaluate(el => el.click(), element);
                                console.log('✅ Successfully clicked "Login anyway" button (alternative method)');
                                await new Promise(resolve => setTimeout(resolve, 5000));
                                return true;
                            } catch (e2) {
                                console.log(`⚠️ Failed to click "Login anyway": ${e2.message}`);
                            }
                        }
                    } else {
                        console.log(`⚠️ Found "Login anyway" button but it's not visible: "${text}"`);
                    }
                }
            } catch (e) {
                // Continue to next element
            }
        }

        console.log('ℹ️ No visible "Login anyway" button found');
        return false;
    }

    async handleLoginAnywayPopup() {
        try {
            console.log('🔍 Handling all popups and "Login anyway" button...');

            // Wait a bit for any popups to appear
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Take screenshot to see current state
            await this.masterPage.screenshot({ path: 'master-session-popup-check.png', fullPage: true });
            console.log('📸 Popup check screenshot saved');

            // Step 1: Close any blocking popups first
            await this.closeBlockingPopups();

            // Step 2: Look for and click "Login anyway" button
            await this.clickLoginAnywayButton();

        } catch (error) {
            console.log('⚠️ Popup handling error:', error.message);

            // Take error screenshot
            await this.masterPage.screenshot({ path: 'master-session-popup-error.png', fullPage: true });
            console.log('📸 Popup error screenshot saved');
        }
    }

    async closeBlockingPopups() {
        console.log('🚫 Closing any blocking popups...');

        // Common close button selectors
        const closeSelectors = [
            'button[aria-label="Close"]',
            'button[title="Close"]',
            '.close-button',
            '.modal-close',
            '.popup-close',
            '[data-testid*="close"]',
            '[data-testid*="dismiss"]',
            'button:contains("×")',
            'button:contains("✕")',
            'button:contains("Close")',
            'button:contains("Dismiss")',
            'button:contains("Cancel")',
            '.fa-times',
            '.fa-close',
            '.icon-close'
        ];

        // Try to close popups multiple times (in case there are multiple layers)
        for (let attempt = 0; attempt < 3; attempt++) {
            console.log(`🔄 Popup closing attempt ${attempt + 1}...`);

            let foundCloseButton = false;

            // Try each close selector
            for (const selector of closeSelectors) {
                try {
                    const closeButtons = await this.masterPage.$$(selector);

                    for (const closeButton of closeButtons) {
                        // Check if button is visible
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none';
                        }, closeButton);

                        if (isVisible) {
                            console.log(`🚫 Found visible close button with selector: ${selector}`);

                            try {
                                await closeButton.click();
                                console.log('✅ Close button clicked');
                                foundCloseButton = true;

                                // Wait for popup to close
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                // Try alternative click method
                                try {
                                    await this.masterPage.evaluate(el => el.click(), closeButton);
                                    console.log('✅ Close button clicked (alternative method)');
                                    foundCloseButton = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    console.log('⚠️ Failed to click close button');
                                }
                            }
                        }
                    }

                    if (foundCloseButton) break;

                } catch (e) {
                    // Continue to next selector
                }
            }

            // Also try to close by text content
            if (!foundCloseButton) {
                const allButtons = await this.masterPage.$$('button, a, div[role="button"], span[role="button"]');

                for (const button of allButtons) {
                    const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);

                    if (text && (
                        text.trim() === '×' ||
                        text.trim() === '✕' ||
                        text.toLowerCase().includes('close') ||
                        text.toLowerCase().includes('dismiss') ||
                        text.toLowerCase().includes('cancel')
                    )) {
                        // Check if button is visible
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none';
                        }, button);

                        if (isVisible) {
                            console.log(`🚫 Found close button by text: "${text.trim()}"`);

                            try {
                                await button.click();
                                console.log('✅ Close button clicked by text');
                                foundCloseButton = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), button);
                                    console.log('✅ Close button clicked by text (alternative method)');
                                    foundCloseButton = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    }
                }
            }

            if (!foundCloseButton) {
                console.log('ℹ️ No more close buttons found');
                break;
            }

            // Take screenshot after each close attempt
            await this.masterPage.screenshot({ path: `master-session-after-close-${attempt + 1}.png`, fullPage: true });
            console.log(`📸 After close attempt ${attempt + 1} screenshot saved`);
        }
    }

    async clickLoginAnywayButton() {
        console.log('🔍 Looking for "Login anyway" button...');

        // Wait a bit after closing popups
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Look for "Login anyway" button with various selectors
        const loginAnywaySelectors = [
            'button:contains("Login anyway")',
            'button:contains("Log in anyway")',
            'button:contains("Continue anyway")',
            '[data-testid*="login-anyway"]',
            '[data-testid*="continue"]',
            'button[value*="anyway"]'
        ];

        let loginAnywayButton = null;

        // Try selector-based search first
        for (const selector of loginAnywaySelectors) {
            try {
                await this.masterPage.waitForSelector(selector, { timeout: 2000 });
                loginAnywayButton = await this.masterPage.$(selector);
                if (loginAnywayButton) {
                    console.log(`✅ Found "Login anyway" button with selector: ${selector}`);
                    break;
                }
            } catch (e) {
                // Continue to next selector
            }
        }

        // If not found by selector, search by text content
        if (!loginAnywayButton) {
            console.log('🔍 Searching for "Login anyway" button by text...');
            const buttons = await this.masterPage.$$('button, a, div[role="button"]');

            for (const button of buttons) {
                const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);
                if (text && (
                    text.toLowerCase().includes('login anyway') ||
                    text.toLowerCase().includes('log in anyway') ||
                    text.toLowerCase().includes('continue anyway')
                )) {
                    // Check if button is visible and clickable
                    const isVisible = await this.masterPage.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 &&
                               style.visibility !== 'hidden' &&
                               style.display !== 'none';
                    }, button);

                    if (isVisible) {
                        loginAnywayButton = button;
                        console.log(`✅ Found "Login anyway" button by text: "${text}"`);
                        break;
                    }
                }
            }
        }

        // Click the button if found
        if (loginAnywayButton) {
            console.log('🔐 Clicking "Login anyway" button...');

            try {
                await loginAnywayButton.click();
                console.log('✅ "Login anyway" button clicked');
            } catch (e) {
                // Try alternative click method
                await this.masterPage.evaluate(el => el.click(), loginAnywayButton);
                console.log('✅ "Login anyway" button clicked (alternative method)');
            }

            // Wait for navigation/popup to close
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot after clicking
            await this.masterPage.screenshot({ path: 'master-session-after-login-anyway.png', fullPage: true });
            console.log('📸 After "Login anyway" screenshot saved');

        } else {
            console.log('ℹ️ No "Login anyway" button found - may already be logged in');
        }
    }

    async verifyAuthentication() {
        let currentUrl = this.masterPage.url();
        console.log(`📍 Current URL: ${currentUrl}`);

        // Wait for dashboard to load
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Check for "Login anyway" popup first
        await this.handleLoginAnywayPopup();

        // Wait a bit more after popup handling
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Get updated URL after popup handling
        currentUrl = this.masterPage.url();
        console.log(`📍 Final URL after popup check: ${currentUrl}`);

        const isOnDashboard = currentUrl.includes('one.dat.com') && !currentUrl.includes('login');
        const pageTitle = await this.masterPage.title();

        console.log(`📄 Page Title: ${pageTitle}`);
        console.log(`🔍 On Dashboard: ${isOnDashboard}`);

        this.isAuthenticated = isOnDashboard && (pageTitle.includes('DAT') || pageTitle.includes('One'));

        if (this.isAuthenticated) {
            console.log('✅ Authentication verified - capturing session data');
            await this.captureSessionData();
            await this.masterPage.screenshot({ path: 'master-session-authenticated.png', fullPage: true });
            console.log('📸 Master session screenshot saved');
        } else {
            console.log('❌ Authentication verification failed');
            await this.masterPage.screenshot({ path: 'master-session-auth-failed.png', fullPage: true });
            console.log('📸 Auth failed screenshot saved');
        }
    }

    async captureSessionData() {
        console.log('📊 Capturing master session data...');
        
        const cookies = await this.masterPage.cookies();
        const storageData = await this.masterPage.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href
            };
        });
        
        this.sessionData = {
            cookies: cookies,
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            timestamp: new Date().toISOString()
        };
        
        // Save to file
        fs.writeFileSync('master-session-data.json', JSON.stringify(this.sessionData, null, 2));
        console.log('✅ Master session data captured and saved');
    }

    async startKeepAlive() {
        console.log('🔄 Starting keep-alive for master session...');

        this.keepAliveInterval = setInterval(async () => {
            try {
                console.log('💓 Keep-alive ping...');

                // Navigate to dashboard to keep session active
                await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'domcontentloaded', timeout: 10000 });

                // Wait a bit for page to load
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Check for any popups during keep-alive
                await this.handleLoginAnywayPopup();

                // Check if still authenticated
                const currentUrl = this.masterPage.url();
                if (currentUrl.includes('login')) {
                    console.log('❌ Master session expired! Need re-authentication.');
                    this.isAuthenticated = false;
                    clearInterval(this.keepAliveInterval);
                } else {
                    console.log('✅ Master session still active');
                    await this.captureSessionData(); // Update session data
                }

            } catch (error) {
                console.error('⚠️ Keep-alive error:', error.message);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    async cloneSessionForUser(userId) {
        if (!this.isAuthenticated || !this.sessionData) {
            throw new Error('Master session not authenticated');
        }
        
        console.log(`👥 Cloning session for user: ${userId}`);
        
        // Create new browser instance for this user
        const userBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security'
            ]
        });
        
        const userPage = await userBrowser.newPage();
        await userPage.setViewport({ width: 1280, height: 720 });
        await userPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Clone cookies
        await userPage.setCookie(...this.sessionData.cookies);
        
        // Clone storage
        await userPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, this.sessionData.localStorage, this.sessionData.sessionStorage);
        
        // Store the cloned session
        this.sessionClones.set(userId, {
            browser: userBrowser,
            page: userPage,
            createdAt: new Date()
        });
        
        console.log(`✅ Session cloned for user: ${userId}`);
        return { browser: userBrowser, page: userPage };
    }

    async getSessionDataForProxy() {
        if (!this.sessionData) {
            throw new Error('No session data available');
        }
        
        // Convert cookies to string format for proxy
        const cookieString = this.sessionData.cookies.map(c => `${c.name}=${c.value}`).join('; ');
        
        return {
            cookies: cookieString,
            localStorage: this.sessionData.localStorage,
            sessionStorage: this.sessionData.sessionStorage,
            currentUrl: this.sessionData.currentUrl,
            timestamp: this.sessionData.timestamp
        };
    }

    async cleanup() {
        console.log('🧹 Cleaning up master session...');
        
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        
        // Close all cloned sessions
        for (const [userId, clone] of this.sessionClones) {
            await clone.browser.close();
        }
        this.sessionClones.clear();
        
        // Close master browser
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        console.log('✅ Cleanup complete');
    }

    async startLiveBrowserServer() {
        console.log('🌐 Starting live browser server...');

        const express = require('express');
        const http = require('http');
        const liveBrowserApp = express();

        // Enable CORS for all routes
        liveBrowserApp.use((req, res, next) => {
            res.header('Access-Control-Allow-Origin', '*');
            res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
            res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept, Authorization');
            if (req.method === 'OPTIONS') {
                res.sendStatus(200);
            } else {
                next();
            }
        });

        // Serve live browser HTML
        liveBrowserApp.get('/live-browser', async (req, res) => {
            try {
                console.log('📡 Serving live browser content...');

                // Get current HTML from the master browser
                const html = await this.masterPage.content();
                const url = this.masterPage.url();

                console.log(`📍 Current URL: ${url}`);
                console.log(`📄 HTML length: ${html.length} chars`);

                // Inject base tag to fix relative URLs
                const baseUrl = new URL(url).origin;
                const modifiedHtml = html.replace(
                    '<head>',
                    `<head><base href="${baseUrl}/">`
                );

                res.setHeader('Content-Type', 'text/html');
                res.send(modifiedHtml);

            } catch (error) {
                console.error('❌ Error serving live browser:', error.message);
                res.status(500).send(`
                    <html>
                        <body style="font-family: Arial; padding: 20px; text-align: center;">
                            <h1>❌ Live Browser Error</h1>
                            <p>Failed to get live browser content: ${error.message}</p>
                        </body>
                    </html>
                `);
            }
        });

        // Serve live browser in iframe wrapper
        liveBrowserApp.get('/live-browser-iframe', async (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Live Master Session</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; }
        .header {
            background: #1a365d; color: white; padding: 10px 20px;
            display: flex; justify-content: space-between; align-items: center;
        }
        .header h1 { font-size: 18px; margin: 0; }
        .status { font-size: 12px; background: #dc2626; padding: 4px 8px; border-radius: 4px; }
        .iframe-container { width: 100%; height: calc(100vh - 50px); }
        .iframe-container iframe { width: 100%; height: 100%; border: none; }
        .loading { display: flex; justify-content: center; align-items: center; height: 200px; font-size: 16px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Live Master Browser</h1>
        <div class="status">🔴 LIVE</div>
    </div>

    <div class="iframe-container">
        <div class="loading" id="loading">Loading live master browser...</div>
        <iframe
            id="live-iframe"
            src="/live-browser"
            style="display: none;"
            onload="document.getElementById('loading').style.display='none'; this.style.display='block';"
            onerror="document.getElementById('loading').innerHTML='❌ Failed to load live browser';"
        ></iframe>
    </div>

    <script>
        // Auto-refresh every 5 seconds to get live updates
        setInterval(() => {
            console.log('🔄 Refreshing live browser...');
            document.getElementById('live-iframe').src = '/live-browser?' + Date.now();
        }, 5000);
    </script>
</body>
</html>`;
            res.send(html);
        });

        // API endpoint to get current browser state
        liveBrowserApp.get('/api/browser-state', async (req, res) => {
            try {
                const url = this.masterPage.url();
                const title = await this.masterPage.title();
                const html = await this.masterPage.content();

                res.json({
                    url: url,
                    title: title,
                    htmlLength: html.length,
                    timestamp: new Date().toISOString(),
                    isAuthenticated: this.isAuthenticated
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });

        // Start the live browser server on port 3005
        const liveBrowserServer = http.createServer(liveBrowserApp);
        liveBrowserServer.listen(3005, '0.0.0.0', () => {
            console.log('🌐 Live Browser Server running on port 3005');
            console.log('🔴 Live Master Browser: http://147.93.146.10:3005/live-browser-iframe');
            console.log('📡 Direct HTML: http://147.93.146.10:3005/live-browser');
            console.log('📊 Browser State API: http://147.93.146.10:3005/api/browser-state');
        });

        this.liveBrowserServer = liveBrowserServer;
    }
}

module.exports = MasterSessionManager;
