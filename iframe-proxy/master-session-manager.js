const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class MasterSessionManager {
    constructor() {
        this.masterBrowser = null;
        this.masterPage = null;
        this.isAuthenticated = false;
        this.sessionData = null;
        this.keepAliveInterval = null;
        this.sessionClones = new Map();
    }

    async initializeMasterBrowser() {
        console.log('🚀 Initializing Master DAT Browser...');
        
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-gpu',
                '--disable-extensions',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        console.log('✅ Master browser initialized');
        return this.masterPage;
    }

    async authenticateMaster(email, password) {
        console.log('🔐 Authenticating Master Browser...');
        
        if (!this.masterPage) {
            await this.initializeMasterBrowser();
        }

        try {
            // Navigate to DAT dashboard (will redirect to login)
            console.log('📍 Navigating to DAT dashboard...');
            await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'networkidle2' });
            
            // Enter email
            await this.masterPage.waitForSelector('input[type="email"], input[name="username"], input[name="email"]', { timeout: 10000 });
            const emailField = await this.masterPage.$('input[type="email"], input[name="username"], input[name="email"]');
            await emailField.click();
            await emailField.type(email, { delay: 100 });
            console.log('✅ Email entered');
            
            // Click Continue
            const continueButton = await this.masterPage.$('button[type="submit"]');
            await continueButton.click();
            await new Promise(resolve => setTimeout(resolve, 3000));
            console.log('✅ Continue clicked');
            
            // Enter password
            await this.masterPage.waitForSelector('input[type="password"]', { timeout: 10000 });
            const passwordField = await this.masterPage.$('input[type="password"]');
            await passwordField.click();
            await passwordField.type(password, { delay: 100 });
            console.log('✅ Password entered');
            
            // Click Login
            const loginButton = await this.masterPage.$('button[type="submit"]');
            await loginButton.click();
            await new Promise(resolve => setTimeout(resolve, 5000));
            console.log('✅ Login clicked');
            
            // Handle 2FA interactively
            console.log('🔐 Checking for 2FA...');
            await this.handle2FAInteractive();
            
            // Verify authentication
            await this.verifyAuthentication();
            
            if (this.isAuthenticated) {
                console.log('🎉 Master browser authenticated successfully!');
                await this.startKeepAlive();
                return true;
            } else {
                throw new Error('Authentication verification failed');
            }
            
        } catch (error) {
            console.error('❌ Master authentication failed:', error.message);
            return false;
        }
    }

    async handle2FAInteractive() {
        try {
            // Look for "Try another method"
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const buttons = await this.masterPage.$$('button, a, span');
            for (const button of buttons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('try another method')) {
                    await button.click();
                    console.log('✅ Clicked "Try another method"');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            
            // Select email option
            const emailButtons = await this.masterPage.$$('button, a, div, span');
            for (const button of emailButtons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('email') && !text.toLowerCase().includes('phone')) {
                    await button.click();
                    console.log('✅ Selected email verification');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    break;
                }
            }
            
            // Wait for code input and prompt user
            await this.masterPage.waitForSelector('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]', { timeout: 15000 });
            
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            console.log('\n📧 EMAIL VERIFICATION CODE NEEDED FOR MASTER SESSION!');
            const emailCode = await new Promise(resolve => {
                rl.question('🔐 Enter the 6-digit verification code: ', resolve);
            });
            rl.close();
            
            // Enter code
            const codeField = await this.masterPage.$('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]');
            await codeField.click();
            await codeField.type(emailCode.trim(), { delay: 100 });
            
            // Click verify
            const verifyButton = await this.masterPage.$('button[type="submit"]');
            await verifyButton.click();
            await new Promise(resolve => setTimeout(resolve, 8000));

            console.log('✅ 2FA completed');

            // Check for "Login anyway" popup
            await this.handleLoginAnywayPopup();
            
        } catch (error) {
            console.log('⚠️ 2FA handling error:', error.message);
        }
    }

    async handleLoginAnywayPopup() {
        try {
            console.log('🔍 Checking for "Login anyway" popup...');

            // Wait a bit for any popups to appear
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Take screenshot to see current state
            await this.masterPage.screenshot({ path: 'master-session-popup-check.png', fullPage: true });
            console.log('📸 Popup check screenshot saved');

            // Look for "Login anyway" button with various selectors
            const loginAnywaySelectors = [
                'button:contains("Login anyway")',
                'button:contains("Log in anyway")',
                'button:contains("Continue anyway")',
                '[data-testid*="login-anyway"]',
                '[data-testid*="continue"]',
                'button[value*="anyway"]'
            ];

            let loginAnywayButton = null;

            // Try selector-based search first
            for (const selector of loginAnywaySelectors) {
                try {
                    await this.masterPage.waitForSelector(selector, { timeout: 2000 });
                    loginAnywayButton = await this.masterPage.$(selector);
                    if (loginAnywayButton) {
                        console.log(`✅ Found "Login anyway" button with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // Continue to next selector
                }
            }

            // If not found by selector, search by text content
            if (!loginAnywayButton) {
                console.log('🔍 Searching for "Login anyway" button by text...');
                const buttons = await this.masterPage.$$('button, a, div[role="button"]');

                for (const button of buttons) {
                    const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);
                    if (text && (
                        text.toLowerCase().includes('login anyway') ||
                        text.toLowerCase().includes('log in anyway') ||
                        text.toLowerCase().includes('continue anyway')
                    )) {
                        loginAnywayButton = button;
                        console.log(`✅ Found "Login anyway" button by text: "${text}"`);
                        break;
                    }
                }
            }

            // Click the button if found
            if (loginAnywayButton) {
                console.log('🔐 Clicking "Login anyway" button...');

                try {
                    await loginAnywayButton.click();
                    console.log('✅ "Login anyway" button clicked');
                } catch (e) {
                    // Try alternative click method
                    await this.masterPage.evaluate(el => el.click(), loginAnywayButton);
                    console.log('✅ "Login anyway" button clicked (alternative method)');
                }

                // Wait for navigation/popup to close
                await new Promise(resolve => setTimeout(resolve, 5000));

                // Take screenshot after clicking
                await this.masterPage.screenshot({ path: 'master-session-after-login-anyway.png', fullPage: true });
                console.log('📸 After "Login anyway" screenshot saved');

            } else {
                console.log('ℹ️ No "Login anyway" popup found - proceeding normally');
            }

        } catch (error) {
            console.log('⚠️ Login anyway popup handling error:', error.message);

            // Take error screenshot
            await this.masterPage.screenshot({ path: 'master-session-popup-error.png', fullPage: true });
            console.log('📸 Popup error screenshot saved');
        }
    }

    async verifyAuthentication() {
        let currentUrl = this.masterPage.url();
        console.log(`📍 Current URL: ${currentUrl}`);

        // Wait for dashboard to load
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Check for "Login anyway" popup first
        await this.handleLoginAnywayPopup();

        // Wait a bit more after popup handling
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Get updated URL after popup handling
        currentUrl = this.masterPage.url();
        console.log(`📍 Final URL after popup check: ${currentUrl}`);

        const isOnDashboard = currentUrl.includes('one.dat.com') && !currentUrl.includes('login');
        const pageTitle = await this.masterPage.title();

        console.log(`📄 Page Title: ${pageTitle}`);
        console.log(`🔍 On Dashboard: ${isOnDashboard}`);

        this.isAuthenticated = isOnDashboard && (pageTitle.includes('DAT') || pageTitle.includes('One'));

        if (this.isAuthenticated) {
            console.log('✅ Authentication verified - capturing session data');
            await this.captureSessionData();
            await this.masterPage.screenshot({ path: 'master-session-authenticated.png', fullPage: true });
            console.log('📸 Master session screenshot saved');
        } else {
            console.log('❌ Authentication verification failed');
            await this.masterPage.screenshot({ path: 'master-session-auth-failed.png', fullPage: true });
            console.log('📸 Auth failed screenshot saved');
        }
    }

    async captureSessionData() {
        console.log('📊 Capturing master session data...');
        
        const cookies = await this.masterPage.cookies();
        const storageData = await this.masterPage.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href
            };
        });
        
        this.sessionData = {
            cookies: cookies,
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            timestamp: new Date().toISOString()
        };
        
        // Save to file
        fs.writeFileSync('master-session-data.json', JSON.stringify(this.sessionData, null, 2));
        console.log('✅ Master session data captured and saved');
    }

    async startKeepAlive() {
        console.log('🔄 Starting keep-alive for master session...');

        this.keepAliveInterval = setInterval(async () => {
            try {
                console.log('💓 Keep-alive ping...');

                // Navigate to dashboard to keep session active
                await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'domcontentloaded', timeout: 10000 });

                // Wait a bit for page to load
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Check for any popups during keep-alive
                await this.handleLoginAnywayPopup();

                // Check if still authenticated
                const currentUrl = this.masterPage.url();
                if (currentUrl.includes('login')) {
                    console.log('❌ Master session expired! Need re-authentication.');
                    this.isAuthenticated = false;
                    clearInterval(this.keepAliveInterval);
                } else {
                    console.log('✅ Master session still active');
                    await this.captureSessionData(); // Update session data
                }

            } catch (error) {
                console.error('⚠️ Keep-alive error:', error.message);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    async cloneSessionForUser(userId) {
        if (!this.isAuthenticated || !this.sessionData) {
            throw new Error('Master session not authenticated');
        }
        
        console.log(`👥 Cloning session for user: ${userId}`);
        
        // Create new browser instance for this user
        const userBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security'
            ]
        });
        
        const userPage = await userBrowser.newPage();
        await userPage.setViewport({ width: 1280, height: 720 });
        await userPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Clone cookies
        await userPage.setCookie(...this.sessionData.cookies);
        
        // Clone storage
        await userPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, this.sessionData.localStorage, this.sessionData.sessionStorage);
        
        // Store the cloned session
        this.sessionClones.set(userId, {
            browser: userBrowser,
            page: userPage,
            createdAt: new Date()
        });
        
        console.log(`✅ Session cloned for user: ${userId}`);
        return { browser: userBrowser, page: userPage };
    }

    async getSessionDataForProxy() {
        if (!this.sessionData) {
            throw new Error('No session data available');
        }
        
        // Convert cookies to string format for proxy
        const cookieString = this.sessionData.cookies.map(c => `${c.name}=${c.value}`).join('; ');
        
        return {
            cookies: cookieString,
            localStorage: this.sessionData.localStorage,
            sessionStorage: this.sessionData.sessionStorage,
            currentUrl: this.sessionData.currentUrl,
            timestamp: this.sessionData.timestamp
        };
    }

    async cleanup() {
        console.log('🧹 Cleaning up master session...');
        
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        
        // Close all cloned sessions
        for (const [userId, clone] of this.sessionClones) {
            await clone.browser.close();
        }
        this.sessionClones.clear();
        
        // Close master browser
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        console.log('✅ Cleanup complete');
    }
}

module.exports = MasterSessionManager;
