const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class MasterSessionManager {
    constructor() {
        this.masterBrowser = null;
        this.masterPage = null;
        this.isAuthenticated = false;
        this.sessionData = null;
        this.keepAliveInterval = null;
        this.sessionClones = new Map();
    }

    async initializeMasterBrowser() {
        console.log('🚀 Initializing Master DAT Browser...');
        
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-gpu',
                '--disable-extensions',
                '--no-first-run',
                '--no-zygote',
                '--single-process',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        console.log('✅ Master browser initialized');
        return this.masterPage;
    }

    async authenticateMaster(email, password) {
        console.log('🔐 Authenticating Master Browser...');
        
        if (!this.masterPage) {
            await this.initializeMasterBrowser();
        }

        try {
            // Navigate to DAT dashboard (will redirect to login)
            console.log('📍 Navigating to DAT dashboard...');
            await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'networkidle2' });
            
            // Enter email
            await this.masterPage.waitForSelector('input[type="email"], input[name="username"], input[name="email"]', { timeout: 10000 });
            const emailField = await this.masterPage.$('input[type="email"], input[name="username"], input[name="email"]');
            await emailField.click();
            await emailField.type(email, { delay: 100 });
            console.log('✅ Email entered');
            
            // Click Continue
            const continueButton = await this.masterPage.$('button[type="submit"]');
            await continueButton.click();
            await new Promise(resolve => setTimeout(resolve, 3000));
            console.log('✅ Continue clicked');
            
            // Enter password
            await this.masterPage.waitForSelector('input[type="password"]', { timeout: 10000 });
            const passwordField = await this.masterPage.$('input[type="password"]');
            await passwordField.click();
            await passwordField.type(password, { delay: 100 });
            console.log('✅ Password entered');
            
            // Click Login
            const loginButton = await this.masterPage.$('button[type="submit"]');
            await loginButton.click();
            await new Promise(resolve => setTimeout(resolve, 5000));
            console.log('✅ Login clicked');
            
            // Handle 2FA interactively
            console.log('🔐 Checking for 2FA...');
            await this.handle2FAInteractive();
            
            // Verify authentication
            await this.verifyAuthentication();
            
            if (this.isAuthenticated) {
                console.log('🎉 Master browser authenticated successfully!');
                await this.startKeepAlive();
                return true;
            } else {
                throw new Error('Authentication verification failed');
            }
            
        } catch (error) {
            console.error('❌ Master authentication failed:', error.message);
            return false;
        }
    }

    async handle2FAInteractive() {
        try {
            // Look for "Try another method"
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            const buttons = await this.masterPage.$$('button, a, span');
            for (const button of buttons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('try another method')) {
                    await button.click();
                    console.log('✅ Clicked "Try another method"');
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    break;
                }
            }
            
            // Select email option
            const emailButtons = await this.masterPage.$$('button, a, div, span');
            for (const button of emailButtons) {
                const text = await this.masterPage.evaluate(el => el.textContent, button);
                if (text && text.toLowerCase().includes('email') && !text.toLowerCase().includes('phone')) {
                    await button.click();
                    console.log('✅ Selected email verification');
                    await new Promise(resolve => setTimeout(resolve, 3000));
                    break;
                }
            }
            
            // Wait for code input and prompt user
            await this.masterPage.waitForSelector('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]', { timeout: 15000 });
            
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            console.log('\n📧 EMAIL VERIFICATION CODE NEEDED FOR MASTER SESSION!');
            const emailCode = await new Promise(resolve => {
                rl.question('🔐 Enter the 6-digit verification code: ', resolve);
            });
            rl.close();
            
            // Enter code
            const codeField = await this.masterPage.$('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]');
            await codeField.click();
            await codeField.type(emailCode.trim(), { delay: 100 });
            
            // Click verify
            const verifyButton = await this.masterPage.$('button[type="submit"]');
            await verifyButton.click();
            console.log('✅ 2FA verify button clicked, waiting for authentication...');

            // Wait longer for 2FA to complete and page to navigate
            await new Promise(resolve => setTimeout(resolve, 15000));

            console.log('✅ 2FA completed');

            // Handle popup sequence properly
            await this.handlePopupSequence();
            
        } catch (error) {
            console.log('⚠️ 2FA handling error:', error.message);
        }
    }

    async handlePopupSequence() {
        console.log('🔄 Starting proper popup sequence handling...');

        try {
            // Step 1: Wait 5 seconds after login to see what popups appear
            console.log('⏳ Step 1: Waiting 5 seconds to see what popups appear...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot to see initial popup state
            await this.masterPage.screenshot({ path: 'popup-sequence-step1-initial.png', fullPage: true });
            console.log('📸 Step 1 screenshot saved: popup-sequence-step1-initial.png');

            // Step 2: Check if we're still on the right page before closing popups
            let currentUrl = this.masterPage.url();
            console.log(`📍 Current URL after 2FA: ${currentUrl}`);

            if (currentUrl.includes('login')) {
                console.log('❌ Still on login page after 2FA - authentication may have failed');
                return;
            }

            // Step 2: Close all popups EXCEPT "Login anyway" (but be more careful)
            console.log('🚫 Step 2: Carefully closing popups except "Login anyway"...');
            await this.closeAllPopupsExceptLoginAnyway();

            // Take screenshot after closing other popups
            await this.masterPage.screenshot({ path: 'popup-sequence-step2-after-close.png', fullPage: true });
            console.log('📸 Step 2 screenshot saved: popup-sequence-step2-after-close.png');

            // Step 3: Wait another 5 seconds for page to settle
            console.log('⏳ Step 3: Waiting 5 seconds for page to settle...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot to see settled state
            await this.masterPage.screenshot({ path: 'popup-sequence-step3-settled.png', fullPage: true });
            console.log('📸 Step 3 screenshot saved: popup-sequence-step3-settled.png');

            // Step 4: Now click "Login anyway" if it's at the top
            console.log('🔐 Step 4: Looking for "Login anyway" button at the top...');
            await this.clickLoginAnywayIfVisible();

            // Take final screenshot
            await this.masterPage.screenshot({ path: 'popup-sequence-step4-final.png', fullPage: true });
            console.log('📸 Step 4 screenshot saved: popup-sequence-step4-final.png');

        } catch (error) {
            console.log('⚠️ Popup sequence error:', error.message);

            // Take error screenshot
            await this.masterPage.screenshot({ path: 'popup-sequence-error.png', fullPage: true });
            console.log('📸 Error screenshot saved: popup-sequence-error.png');
        }
    }

    async closeAllPopupsExceptLoginAnyway() {
        console.log('🚫 Closing all blocking popups except "Login anyway"...');

        // Multiple rounds to handle layered popups
        for (let round = 0; round < 5; round++) {
            console.log(`🔄 Popup closing round ${round + 1}...`);

            let foundPopup = false;

            // First, look for obvious close buttons (X, Close, etc.) - be more aggressive
            const closeSelectors = [
                // Standard close button selectors
                'button[aria-label="Close"]',
                'button[title="Close"]',
                'button[aria-label="close"]',
                'button[title="close"]',
                '.close-button',
                '.modal-close',
                '.popup-close',
                '.dialog-close',
                '[data-testid*="close"]',
                '[data-testid*="dismiss"]',

                // X symbols in various elements
                'button:contains("×")',
                'button:contains("✕")',
                'span:contains("×")',
                'span:contains("✕")',
                'div:contains("×")',
                'div:contains("✕")',
                'a:contains("×")',
                'a:contains("✕")',

                // Common modal/popup patterns
                '.modal button',
                '.popup button',
                '.dialog button',
                '.overlay button',
                '[role="dialog"] button',
                '[role="alertdialog"] button',

                // Icon-based close buttons
                '.fa-times',
                '.fa-close',
                '.fa-x',
                '.icon-close',
                '.icon-times',
                '.icon-x',

                // Generic selectors for any clickable element with close-like appearance
                '*[class*="close"]',
                '*[id*="close"]',
                '*[class*="dismiss"]',
                '*[id*="dismiss"]'
            ];

            for (const selector of closeSelectors) {
                try {
                    const closeButtons = await this.masterPage.$$(selector);

                    for (const closeButton of closeButtons) {
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none' &&
                                   style.opacity !== '0';
                        }, closeButton);

                        if (isVisible) {
                            console.log(`🚫 Found close button with selector: ${selector}`);

                            try {
                                await closeButton.click();
                                console.log(`✅ Clicked close button`);
                                foundPopup = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), closeButton);
                                    console.log(`✅ Clicked close button (alternative)`);
                                    foundPopup = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    }

                    if (foundPopup) break;

                } catch (e) {
                    // Continue to next selector
                }
            }

            // If no obvious close buttons, try clicking elements in top-right corners (where close buttons usually are)
            if (!foundPopup) {
                console.log('🔍 Looking for close buttons in top-right corners...');

                const allClickableElements = await this.masterPage.$$('button, a, span, div, i, svg, [role="button"]');

                for (const element of allClickableElements) {
                    try {
                        const elementInfo = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);

                            return {
                                isVisible: rect.width > 0 && rect.height > 0 &&
                                          style.visibility !== 'hidden' &&
                                          style.display !== 'none' &&
                                          style.opacity !== '0',
                                top: rect.top,
                                left: rect.left,
                                right: rect.right,
                                width: rect.width,
                                height: rect.height,
                                className: el.className,
                                id: el.id,
                                tagName: el.tagName,
                                textContent: (el.textContent || '').trim()
                            };
                        }, element);

                        // Look for small clickable elements in the top area that might be close buttons
                        if (elementInfo.isVisible &&
                            elementInfo.top < 600 && // In upper part of screen
                            elementInfo.width < 50 && // Small width (typical for close buttons)
                            elementInfo.height < 50 && // Small height
                            (
                                elementInfo.textContent === '×' ||
                                elementInfo.textContent === '✕' ||
                                elementInfo.textContent === '' || // Empty (might be icon)
                                elementInfo.className.toLowerCase().includes('close') ||
                                elementInfo.className.toLowerCase().includes('dismiss') ||
                                elementInfo.id.toLowerCase().includes('close') ||
                                elementInfo.id.toLowerCase().includes('dismiss')
                            )
                        ) {
                            console.log(`🎯 Found potential close button: ${elementInfo.tagName} at (${elementInfo.top}, ${elementInfo.left}) - "${elementInfo.textContent}" class="${elementInfo.className}"`);

                            try {
                                await element.click();
                                console.log(`✅ Clicked potential close button`);
                                foundPopup = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), element);
                                    console.log(`✅ Clicked potential close button (alternative)`);
                                    foundPopup = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    } catch (e) {
                        // Continue to next element
                    }
                }
            }

            // If still no popup found, look for dismissible elements by text
            if (!foundPopup) {
                const allElements = await this.masterPage.$$('button, a, div, span, [role="button"], [role="dialog"]');

                for (const element of allElements) {
                    try {
                        const text = await this.masterPage.evaluate(el => el.textContent || el.innerText || el.title || el.getAttribute('aria-label'), element);

                        if (text) {
                            const lowerText = text.toLowerCase().trim();

                            // Skip "Login anyway" button - we'll handle it later
                            if (lowerText.includes('login anyway') || lowerText.includes('log in anyway')) {
                                console.log(`⏭️ Skipping "Login anyway" button: "${text.substring(0, 50)}..."`);
                                continue;
                            }

                            // Look for dismissible content
                            if (
                                lowerText === '×' ||
                                lowerText === '✕' ||
                                lowerText.includes('close') ||
                                lowerText.includes('dismiss') ||
                                lowerText.includes('no thanks') ||
                                lowerText.includes('skip') ||
                                lowerText.includes('cancel') ||
                                lowerText.includes('not now') ||
                                lowerText.includes('maybe later') ||
                                lowerText.includes('post your trucks') ||
                                lowerText.includes('click here to post') ||
                                lowerText.includes('get started') ||
                                lowerText.includes('learn more') ||
                                lowerText.includes('got it') ||
                                lowerText.includes('ok') ||
                                lowerText.includes('continue') ||
                                lowerText.includes('next time')
                            ) {
                                // Check if element is visible and clickable
                                const isVisible = await this.masterPage.evaluate(el => {
                                    const rect = el.getBoundingClientRect();
                                    const style = window.getComputedStyle(el);
                                    return rect.width > 0 && rect.height > 0 &&
                                           style.visibility !== 'hidden' &&
                                           style.display !== 'none' &&
                                           style.opacity !== '0';
                                }, element);

                                if (isVisible) {
                                    console.log(`🚫 Closing popup/element: "${text.substring(0, 50)}..."`);

                                    try {
                                        await element.click();
                                        console.log(`✅ Clicked dismissible element`);
                                        foundPopup = true;
                                        await new Promise(resolve => setTimeout(resolve, 2000));
                                        break;
                                    } catch (e) {
                                        try {
                                            await this.masterPage.evaluate(el => el.click(), element);
                                            console.log(`✅ Clicked dismissible element (alternative)`);
                                            foundPopup = true;
                                            await new Promise(resolve => setTimeout(resolve, 2000));
                                            break;
                                        } catch (e2) {
                                            // Continue
                                        }
                                    }
                                }
                            }
                        }
                    } catch (e) {
                        // Continue to next element
                    }
                }
            }

            // Nuclear option: try pressing Escape key to close modals
            if (!foundPopup && round === 0) {
                console.log('💥 Nuclear option: Pressing Escape key to close modals...');
                try {
                    await this.masterPage.keyboard.press('Escape');
                    console.log('✅ Pressed Escape key');
                    foundPopup = true;
                    await new Promise(resolve => setTimeout(resolve, 2000));
                } catch (e) {
                    console.log('⚠️ Failed to press Escape key');
                }
            }

            // Add detailed debugging for this round
            if (!foundPopup) {
                console.log('🔍 No popup found, analyzing page elements...');

                const pageAnalysis = await this.masterPage.evaluate(() => {
                    const elements = [];
                    const allNodes = document.querySelectorAll('*');

                    for (let i = 0; i < Math.min(allNodes.length, 100); i++) { // Limit to first 100 for performance
                        const el = allNodes[i];
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);

                        if (rect.width > 0 && rect.height > 0 &&
                            style.visibility !== 'hidden' &&
                            style.display !== 'none' &&
                            (
                                (el.textContent && (el.textContent.includes('×') || el.textContent.includes('✕') || el.textContent.toLowerCase().includes('close'))) ||
                                el.className.toLowerCase().includes('close') ||
                                el.className.toLowerCase().includes('modal') ||
                                el.className.toLowerCase().includes('popup') ||
                                (style.position === 'fixed' && parseInt(style.zIndex) > 100)
                            )
                        ) {
                            elements.push({
                                tagName: el.tagName,
                                className: el.className,
                                id: el.id,
                                textContent: (el.textContent || '').trim().substring(0, 50),
                                top: rect.top,
                                left: rect.left,
                                width: rect.width,
                                height: rect.height,
                                zIndex: style.zIndex,
                                position: style.position
                            });
                        }
                    }

                    return elements;
                });

                console.log(`🔍 Found ${pageAnalysis.length} potentially relevant elements:`);
                pageAnalysis.forEach((el, index) => {
                    console.log(`   ${index + 1}. ${el.tagName} - "${el.textContent}" - class:"${el.className}" - pos:(${el.top},${el.left}) - z:${el.zIndex}`);
                });
            }

            // Take screenshot after each round
            await this.masterPage.screenshot({ path: `popup-close-round-${round + 1}.png`, fullPage: true });
            console.log(`📸 Popup close round ${round + 1} screenshot saved`);

            // If no popups found in this round, we're done
            if (!foundPopup) {
                console.log(`ℹ️ No more popups found in round ${round + 1}`);
                break;
            }
        }
    }

    async clickLoginAnywayIfVisible() {
        console.log('🔍 Looking for visible "Login anyway" button...');

        const allElements = await this.masterPage.$$('button, a, div[role="button"], span[role="button"]');

        for (const element of allElements) {
            try {
                const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, element);

                if (text && (
                    text.toLowerCase().includes('login anyway') ||
                    text.toLowerCase().includes('log in anyway')
                )) {
                    // Check if element is visible and at the top (not blocked)
                    const elementInfo = await this.masterPage.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);

                        return {
                            isVisible: rect.width > 0 && rect.height > 0 &&
                                      style.visibility !== 'hidden' &&
                                      style.display !== 'none' &&
                                      style.opacity !== '0',
                            top: rect.top,
                            left: rect.left,
                            zIndex: style.zIndex
                        };
                    }, element);

                    if (elementInfo.isVisible) {
                        console.log(`✅ Found visible "Login anyway" button: "${text}"`);
                        console.log(`📍 Position: top=${elementInfo.top}, left=${elementInfo.left}, zIndex=${elementInfo.zIndex}`);

                        try {
                            await element.click();
                            console.log('✅ Successfully clicked "Login anyway" button');

                            // Wait for navigation
                            await new Promise(resolve => setTimeout(resolve, 5000));

                            return true;

                        } catch (e) {
                            // Try alternative click method
                            try {
                                await this.masterPage.evaluate(el => el.click(), element);
                                console.log('✅ Successfully clicked "Login anyway" button (alternative method)');
                                await new Promise(resolve => setTimeout(resolve, 5000));
                                return true;
                            } catch (e2) {
                                console.log(`⚠️ Failed to click "Login anyway": ${e2.message}`);
                            }
                        }
                    } else {
                        console.log(`⚠️ Found "Login anyway" button but it's not visible: "${text}"`);
                    }
                }
            } catch (e) {
                // Continue to next element
            }
        }

        console.log('ℹ️ No visible "Login anyway" button found');
        return false;
    }

    async handleLoginAnywayPopup() {
        try {
            console.log('🔍 Handling all popups and "Login anyway" button...');

            // Wait a bit for any popups to appear
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Take screenshot to see current state
            await this.masterPage.screenshot({ path: 'master-session-popup-check.png', fullPage: true });
            console.log('📸 Popup check screenshot saved');

            // Step 1: Close any blocking popups first
            await this.closeBlockingPopups();

            // Step 2: Look for and click "Login anyway" button
            await this.clickLoginAnywayButton();

        } catch (error) {
            console.log('⚠️ Popup handling error:', error.message);

            // Take error screenshot
            await this.masterPage.screenshot({ path: 'master-session-popup-error.png', fullPage: true });
            console.log('📸 Popup error screenshot saved');
        }
    }

    async closeBlockingPopups() {
        console.log('🚫 Closing any blocking popups...');

        // Common close button selectors
        const closeSelectors = [
            'button[aria-label="Close"]',
            'button[title="Close"]',
            '.close-button',
            '.modal-close',
            '.popup-close',
            '[data-testid*="close"]',
            '[data-testid*="dismiss"]',
            'button:contains("×")',
            'button:contains("✕")',
            'button:contains("Close")',
            'button:contains("Dismiss")',
            'button:contains("Cancel")',
            '.fa-times',
            '.fa-close',
            '.icon-close'
        ];

        // Try to close popups multiple times (in case there are multiple layers)
        for (let attempt = 0; attempt < 3; attempt++) {
            console.log(`🔄 Popup closing attempt ${attempt + 1}...`);

            let foundCloseButton = false;

            // Try each close selector
            for (const selector of closeSelectors) {
                try {
                    const closeButtons = await this.masterPage.$$(selector);

                    for (const closeButton of closeButtons) {
                        // Check if button is visible
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none';
                        }, closeButton);

                        if (isVisible) {
                            console.log(`🚫 Found visible close button with selector: ${selector}`);

                            try {
                                await closeButton.click();
                                console.log('✅ Close button clicked');
                                foundCloseButton = true;

                                // Wait for popup to close
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                // Try alternative click method
                                try {
                                    await this.masterPage.evaluate(el => el.click(), closeButton);
                                    console.log('✅ Close button clicked (alternative method)');
                                    foundCloseButton = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    console.log('⚠️ Failed to click close button');
                                }
                            }
                        }
                    }

                    if (foundCloseButton) break;

                } catch (e) {
                    // Continue to next selector
                }
            }

            // Also try to close by text content
            if (!foundCloseButton) {
                const allButtons = await this.masterPage.$$('button, a, div[role="button"], span[role="button"]');

                for (const button of allButtons) {
                    const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);

                    if (text && (
                        text.trim() === '×' ||
                        text.trim() === '✕' ||
                        text.toLowerCase().includes('close') ||
                        text.toLowerCase().includes('dismiss') ||
                        text.toLowerCase().includes('cancel')
                    )) {
                        // Check if button is visible
                        const isVisible = await this.masterPage.evaluate(el => {
                            const rect = el.getBoundingClientRect();
                            const style = window.getComputedStyle(el);
                            return rect.width > 0 && rect.height > 0 &&
                                   style.visibility !== 'hidden' &&
                                   style.display !== 'none';
                        }, button);

                        if (isVisible) {
                            console.log(`🚫 Found close button by text: "${text.trim()}"`);

                            try {
                                await button.click();
                                console.log('✅ Close button clicked by text');
                                foundCloseButton = true;
                                await new Promise(resolve => setTimeout(resolve, 2000));
                                break;
                            } catch (e) {
                                try {
                                    await this.masterPage.evaluate(el => el.click(), button);
                                    console.log('✅ Close button clicked by text (alternative method)');
                                    foundCloseButton = true;
                                    await new Promise(resolve => setTimeout(resolve, 2000));
                                    break;
                                } catch (e2) {
                                    // Continue
                                }
                            }
                        }
                    }
                }
            }

            if (!foundCloseButton) {
                console.log('ℹ️ No more close buttons found');
                break;
            }

            // Take screenshot after each close attempt
            await this.masterPage.screenshot({ path: `master-session-after-close-${attempt + 1}.png`, fullPage: true });
            console.log(`📸 After close attempt ${attempt + 1} screenshot saved`);
        }
    }

    async clickLoginAnywayButton() {
        console.log('🔍 Looking for "Login anyway" button...');

        // Wait a bit after closing popups
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Look for "Login anyway" button with various selectors
        const loginAnywaySelectors = [
            'button:contains("Login anyway")',
            'button:contains("Log in anyway")',
            'button:contains("Continue anyway")',
            '[data-testid*="login-anyway"]',
            '[data-testid*="continue"]',
            'button[value*="anyway"]'
        ];

        let loginAnywayButton = null;

        // Try selector-based search first
        for (const selector of loginAnywaySelectors) {
            try {
                await this.masterPage.waitForSelector(selector, { timeout: 2000 });
                loginAnywayButton = await this.masterPage.$(selector);
                if (loginAnywayButton) {
                    console.log(`✅ Found "Login anyway" button with selector: ${selector}`);
                    break;
                }
            } catch (e) {
                // Continue to next selector
            }
        }

        // If not found by selector, search by text content
        if (!loginAnywayButton) {
            console.log('🔍 Searching for "Login anyway" button by text...');
            const buttons = await this.masterPage.$$('button, a, div[role="button"]');

            for (const button of buttons) {
                const text = await this.masterPage.evaluate(el => el.textContent || el.innerText, button);
                if (text && (
                    text.toLowerCase().includes('login anyway') ||
                    text.toLowerCase().includes('log in anyway') ||
                    text.toLowerCase().includes('continue anyway')
                )) {
                    // Check if button is visible and clickable
                    const isVisible = await this.masterPage.evaluate(el => {
                        const rect = el.getBoundingClientRect();
                        const style = window.getComputedStyle(el);
                        return rect.width > 0 && rect.height > 0 &&
                               style.visibility !== 'hidden' &&
                               style.display !== 'none';
                    }, button);

                    if (isVisible) {
                        loginAnywayButton = button;
                        console.log(`✅ Found "Login anyway" button by text: "${text}"`);
                        break;
                    }
                }
            }
        }

        // Click the button if found
        if (loginAnywayButton) {
            console.log('🔐 Clicking "Login anyway" button...');

            try {
                await loginAnywayButton.click();
                console.log('✅ "Login anyway" button clicked');
            } catch (e) {
                // Try alternative click method
                await this.masterPage.evaluate(el => el.click(), loginAnywayButton);
                console.log('✅ "Login anyway" button clicked (alternative method)');
            }

            // Wait for navigation/popup to close
            await new Promise(resolve => setTimeout(resolve, 5000));

            // Take screenshot after clicking
            await this.masterPage.screenshot({ path: 'master-session-after-login-anyway.png', fullPage: true });
            console.log('📸 After "Login anyway" screenshot saved');

        } else {
            console.log('ℹ️ No "Login anyway" button found - may already be logged in');
        }
    }

    async verifyAuthentication() {
        let currentUrl = this.masterPage.url();
        console.log(`📍 Current URL: ${currentUrl}`);

        // Wait for dashboard to load
        await new Promise(resolve => setTimeout(resolve, 8000));

        // Check for "Login anyway" popup first
        await this.handleLoginAnywayPopup();

        // Wait a bit more after popup handling
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Get updated URL after popup handling
        currentUrl = this.masterPage.url();
        console.log(`📍 Final URL after popup check: ${currentUrl}`);

        const isOnDashboard = currentUrl.includes('one.dat.com') && !currentUrl.includes('login');
        const pageTitle = await this.masterPage.title();

        console.log(`📄 Page Title: ${pageTitle}`);
        console.log(`🔍 On Dashboard: ${isOnDashboard}`);

        this.isAuthenticated = isOnDashboard && (pageTitle.includes('DAT') || pageTitle.includes('One'));

        if (this.isAuthenticated) {
            console.log('✅ Authentication verified - capturing session data');
            await this.captureSessionData();
            await this.masterPage.screenshot({ path: 'master-session-authenticated.png', fullPage: true });
            console.log('📸 Master session screenshot saved');
        } else {
            console.log('❌ Authentication verification failed');
            await this.masterPage.screenshot({ path: 'master-session-auth-failed.png', fullPage: true });
            console.log('📸 Auth failed screenshot saved');
        }
    }

    async captureSessionData() {
        console.log('📊 Capturing master session data...');
        
        const cookies = await this.masterPage.cookies();
        const storageData = await this.masterPage.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href
            };
        });
        
        this.sessionData = {
            cookies: cookies,
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            timestamp: new Date().toISOString()
        };
        
        // Save to file
        fs.writeFileSync('master-session-data.json', JSON.stringify(this.sessionData, null, 2));
        console.log('✅ Master session data captured and saved');
    }

    async startKeepAlive() {
        console.log('🔄 Starting keep-alive for master session...');

        this.keepAliveInterval = setInterval(async () => {
            try {
                console.log('💓 Keep-alive ping...');

                // Navigate to dashboard to keep session active
                await this.masterPage.goto('https://one.dat.com/dashboard', { waitUntil: 'domcontentloaded', timeout: 10000 });

                // Wait a bit for page to load
                await new Promise(resolve => setTimeout(resolve, 3000));

                // Check for any popups during keep-alive
                await this.handleLoginAnywayPopup();

                // Check if still authenticated
                const currentUrl = this.masterPage.url();
                if (currentUrl.includes('login')) {
                    console.log('❌ Master session expired! Need re-authentication.');
                    this.isAuthenticated = false;
                    clearInterval(this.keepAliveInterval);
                } else {
                    console.log('✅ Master session still active');
                    await this.captureSessionData(); // Update session data
                }

            } catch (error) {
                console.error('⚠️ Keep-alive error:', error.message);
            }
        }, 5 * 60 * 1000); // Every 5 minutes
    }

    async cloneSessionForUser(userId) {
        if (!this.isAuthenticated || !this.sessionData) {
            throw new Error('Master session not authenticated');
        }
        
        console.log(`👥 Cloning session for user: ${userId}`);
        
        // Create new browser instance for this user
        const userBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security'
            ]
        });
        
        const userPage = await userBrowser.newPage();
        await userPage.setViewport({ width: 1280, height: 720 });
        await userPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Clone cookies
        await userPage.setCookie(...this.sessionData.cookies);
        
        // Clone storage
        await userPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            const localStorage = JSON.parse(localStorageData);
            const sessionStorage = JSON.parse(sessionStorageData);
            
            for (const [key, value] of Object.entries(localStorage)) {
                window.localStorage.setItem(key, value);
            }
            
            for (const [key, value] of Object.entries(sessionStorage)) {
                window.sessionStorage.setItem(key, value);
            }
        }, this.sessionData.localStorage, this.sessionData.sessionStorage);
        
        // Store the cloned session
        this.sessionClones.set(userId, {
            browser: userBrowser,
            page: userPage,
            createdAt: new Date()
        });
        
        console.log(`✅ Session cloned for user: ${userId}`);
        return { browser: userBrowser, page: userPage };
    }

    async getSessionDataForProxy() {
        if (!this.sessionData) {
            throw new Error('No session data available');
        }
        
        // Convert cookies to string format for proxy
        const cookieString = this.sessionData.cookies.map(c => `${c.name}=${c.value}`).join('; ');
        
        return {
            cookies: cookieString,
            localStorage: this.sessionData.localStorage,
            sessionStorage: this.sessionData.sessionStorage,
            currentUrl: this.sessionData.currentUrl,
            timestamp: this.sessionData.timestamp
        };
    }

    async cleanup() {
        console.log('🧹 Cleaning up master session...');
        
        if (this.keepAliveInterval) {
            clearInterval(this.keepAliveInterval);
        }
        
        // Close all cloned sessions
        for (const [userId, clone] of this.sessionClones) {
            await clone.browser.close();
        }
        this.sessionClones.clear();
        
        // Close master browser
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        
        console.log('✅ Cleanup complete');
    }
}

module.exports = MasterSessionManager;
