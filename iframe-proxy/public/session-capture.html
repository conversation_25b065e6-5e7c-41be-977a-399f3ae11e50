<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Session Capture Tool</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #2d2d2d;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #0066cc;
        }
        
        .step h3 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .code-block {
            background: #2d2d2d;
            color: #00ff88;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #000;
        }
        
        .input-group {
            margin: 15px 0;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        .input-group textarea {
            width: 100%;
            height: 100px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .result {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
        
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 DAT Session Capture Tool</h1>
            <p>Extract authentication data from a logged-in DAT session to enable pre-authenticated access</p>
        </div>
        
        <div class="step">
            <h3>📋 Step 1: Login to DAT Manually</h3>
            <p>First, you need to login to DAT One in a regular browser tab:</p>
            <ol>
                <li>Open a new tab and go to <strong>https://login.dat.com</strong></li>
                <li>Login with your DAT credentials</li>
                <li>Navigate to the DAT One dashboard</li>
                <li>Make sure you can see the full interface (not the loading screen)</li>
            </ol>
        </div>
        
        <div class="step">
            <h3>🔍 Step 2: Extract Session Data</h3>
            <p>Once logged in, extract the following data from your browser:</p>
            
            <h4>Method A: Using Browser Developer Tools</h4>
            <div class="code-block">
1. Press F12 to open Developer Tools
2. Go to "Application" tab → "Cookies" → "https://one.dat.com"
3. Copy all cookie values
4. Go to "Network" tab → Refresh the page
5. Click on any request → "Headers" → Copy "Authorization" header
            </div>
            
            <h4>Method B: Using JavaScript Console</h4>
            <div class="code-block">
// Run this in the console on the logged-in DAT page:
console.log('=== DAT SESSION DATA ===');
console.log('Cookies:', document.cookie);
console.log('Local Storage:', JSON.stringify(localStorage));
console.log('Session Storage:', JSON.stringify(sessionStorage));
console.log('Current URL:', window.location.href);
            </div>
            
            <button class="btn" onclick="copyConsoleScript()">📋 Copy Console Script</button>
        </div>
        
        <div class="step">
            <h3>📝 Step 3: Paste Session Data Here</h3>
            
            <div class="input-group">
                <label for="cookies">Cookies (document.cookie):</label>
                <textarea id="cookies" placeholder="Paste all cookies here..."></textarea>
            </div>
            
            <div class="input-group">
                <label for="localStorage">Local Storage:</label>
                <textarea id="localStorage" placeholder="Paste localStorage JSON here..."></textarea>
            </div>
            
            <div class="input-group">
                <label for="sessionStorage">Session Storage:</label>
                <textarea id="sessionStorage" placeholder="Paste sessionStorage JSON here..."></textarea>
            </div>
            
            <div class="input-group">
                <label for="authHeader">Authorization Header (if any):</label>
                <input type="text" id="authHeader" placeholder="Bearer token or other auth header...">
            </div>
            
            <div class="input-group">
                <label for="currentUrl">Current DAT URL:</label>
                <input type="text" id="currentUrl" placeholder="https://one.dat.com/dashboard">
            </div>
            
            <button class="btn success" onclick="processSessionData()">🔧 Process Session Data</button>
            <button class="btn warning" onclick="testSession()">🧪 Test Session</button>
        </div>
        
        <div class="step">
            <h3>🚀 Step 4: Generated Proxy Configuration</h3>
            <p>Once you provide the session data, we'll generate a proxy configuration that includes your authentication:</p>
            
            <div id="result" style="display: none;">
                <div class="result">
                    <h4>✅ Session Data Processed Successfully!</h4>
                    <p>Your authenticated proxy is ready. Use this URL:</p>
                    <div class="code-block" id="proxyUrl"></div>
                </div>
            </div>
            
            <div id="error" style="display: none;">
                <div class="error">
                    <h4>❌ Error Processing Session Data</h4>
                    <p id="errorMessage"></p>
                </div>
            </div>
        </div>
        
        <div class="step">
            <h3>📚 What This Tool Does</h3>
            <ul>
                <li><strong>Extracts Authentication:</strong> Captures cookies, tokens, and session data</li>
                <li><strong>Creates Authenticated Proxy:</strong> Builds a proxy that includes your session</li>
                <li><strong>Enables Multi-User:</strong> Each user can provide their own session data</li>
                <li><strong>Bypasses Login:</strong> Direct access to authenticated DAT dashboard</li>
            </ul>
        </div>
    </div>

    <script>
        function copyConsoleScript() {
            const script = `
console.log('=== DAT SESSION DATA ===');
console.log('Cookies:', document.cookie);
console.log('Local Storage:', JSON.stringify(localStorage));
console.log('Session Storage:', JSON.stringify(sessionStorage));
console.log('Current URL:', window.location.href);
console.log('=== END SESSION DATA ===');
            `.trim();
            
            navigator.clipboard.writeText(script).then(() => {
                alert('Console script copied! Paste it in the DAT browser console.');
            });
        }
        
        function processSessionData() {
            const cookies = document.getElementById('cookies').value;
            const localStorage = document.getElementById('localStorage').value;
            const sessionStorage = document.getElementById('sessionStorage').value;
            const authHeader = document.getElementById('authHeader').value;
            const currentUrl = document.getElementById('currentUrl').value;
            
            if (!cookies && !authHeader) {
                showError('Please provide at least cookies or authorization header');
                return;
            }
            
            // Process the session data
            const sessionData = {
                cookies: cookies,
                localStorage: localStorage,
                sessionStorage: sessionStorage,
                authHeader: authHeader,
                currentUrl: currentUrl,
                timestamp: new Date().toISOString()
            };
            
            // Send to server to create authenticated proxy
            fetch('/api/create-authenticated-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(sessionData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showResult(data.proxyUrl, data.sessionId);
                } else {
                    showError(data.error);
                }
            })
            .catch(error => {
                showError('Failed to process session data: ' + error.message);
            });
        }
        
        function testSession() {
            const cookies = document.getElementById('cookies').value;
            const authHeader = document.getElementById('authHeader').value;
            
            if (!cookies && !authHeader) {
                showError('Please provide session data first');
                return;
            }
            
            // Test the session by making a request
            fetch('/api/test-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    cookies: cookies,
                    authHeader: authHeader
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.valid) {
                    showResult('Session is valid! Ready to create authenticated proxy.', null);
                } else {
                    showError('Session appears to be invalid or expired: ' + data.reason);
                }
            })
            .catch(error => {
                showError('Failed to test session: ' + error.message);
            });
        }
        
        function showResult(message, sessionId) {
            const resultDiv = document.getElementById('result');
            const errorDiv = document.getElementById('error');
            const proxyUrlDiv = document.getElementById('proxyUrl');
            
            errorDiv.style.display = 'none';
            resultDiv.style.display = 'block';
            
            if (sessionId) {
                proxyUrlDiv.textContent = `http://147.93.146.10:3004/authenticated/${sessionId}`;
            } else {
                proxyUrlDiv.textContent = message;
            }
        }
        
        function showError(message) {
            const resultDiv = document.getElementById('result');
            const errorDiv = document.getElementById('error');
            const errorMessageDiv = document.getElementById('errorMessage');
            
            resultDiv.style.display = 'none';
            errorDiv.style.display = 'block';
            errorMessageDiv.textContent = message;
        }
    </script>
</body>
</html>
