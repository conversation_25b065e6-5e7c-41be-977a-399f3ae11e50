<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT iframe Proxy Test - WORKING!</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .header {
            background: #2d2d2d;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .controls {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .url-display {
            font-family: monospace;
            background: #e9ecef;
            padding: 8px;
            border-radius: 4px;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 DAT iframe Proxy Test</h1>
        <p>Testing DAT websites through reverse proxy to bypass X-Frame-Options</p>
    </div>
    
    <div class="test-section">
        <h2>🎯 DAT One Dashboard (Proxied)</h2>
        <div class="url-display">Proxy URL: /dat-one/dashboard</div>
        <div class="url-display">Target: https://one.dat.com/dashboard</div>
        
        <div class="controls">
            <button class="btn" onclick="loadDatOne()">Load DAT One Dashboard</button>
            <button class="btn" onclick="loadDatOneLogin()">Load DAT One Login</button>
            <button class="btn" onclick="loadDatOneSearch()">Load Search Loads</button>
            <button class="btn success" onclick="forceRefresh('dat-one')">🔄 Force Refresh</button>
        </div>
        
        <div id="dat-one-status" class="status loading">Ready to load...</div>
        <iframe id="dat-one-frame" src="about:blank" onload="handleLoad('dat-one')"></iframe>
    </div>
    
    <div class="test-section">
        <h2>🔐 DAT Login (Proxied)</h2>
        <div class="url-display">Proxy URL: /dat-login</div>
        <div class="url-display">Target: https://login.dat.com</div>
        
        <div class="controls">
            <button class="btn" onclick="loadDatLogin()">Load DAT Login</button>
            <button class="btn success" onclick="forceRefresh('dat-login')">🔄 Force Refresh</button>
        </div>
        
        <div id="dat-login-status" class="status loading">Ready to load...</div>
        <iframe id="dat-login-frame" src="about:blank" onload="handleLoad('dat-login')"></iframe>
    </div>
    
    <div class="test-section">
        <h2>🌐 DAT Main Site (Proxied)</h2>
        <div class="url-display">Proxy URL: /dat-main</div>
        <div class="url-display">Target: https://www.dat.com</div>
        
        <div class="controls">
            <button class="btn" onclick="loadDatMain()">Load DAT Main Site</button>
        </div>
        
        <div id="dat-main-status" class="status loading">Ready to load...</div>
        <iframe id="dat-main-frame" src="about:blank" onload="handleLoad('dat-main')"></iframe>
    </div>

    <script>
        function updateStatus(frameId, message, type = 'loading') {
            const statusEl = document.getElementById(frameId + '-status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        function loadDatOne() {
            updateStatus('dat-one', '🔄 Loading DAT One Dashboard...', 'loading');
            document.getElementById('dat-one-frame').src = '/dat-one/dashboard';
        }

        function loadDatOneLogin() {
            updateStatus('dat-one', '🔄 Loading DAT One Login...', 'loading');
            document.getElementById('dat-one-frame').src = '/dat-one/login';
        }

        function loadDatOneSearch() {
            updateStatus('dat-one', '🔄 Loading Search Loads...', 'loading');
            document.getElementById('dat-one-frame').src = '/dat-one/search-loads-ow';
        }

        function loadDatLogin() {
            updateStatus('dat-login', '🔄 Loading DAT Login...', 'loading');
            document.getElementById('dat-login-frame').src = '/dat-login';
        }

        function loadDatMain() {
            updateStatus('dat-main', '🔄 Loading DAT Main Site...', 'loading');
            document.getElementById('dat-main-frame').src = '/dat-main';
        }

        function handleLoad(frameId) {
            const iframe = document.getElementById(frameId + '-frame');

            try {
                // Check if iframe loaded successfully
                if (iframe.src !== 'about:blank') {
                    updateStatus(frameId, '✅ Loaded successfully! Checking for loading screen...', 'success');
                    console.log(`${frameId}: iframe loaded successfully`);

                    // Check for stuck loading screen after a delay
                    setTimeout(() => checkForLoadingScreen(frameId), 5000);
                    setTimeout(() => checkForLoadingScreen(frameId), 10000);
                    setTimeout(() => checkForLoadingScreen(frameId), 15000);
                }
            } catch (e) {
                console.log(`${frameId}: Cross-origin access blocked (expected)`);
            }
        }

        function checkForLoadingScreen(frameId) {
            const iframe = document.getElementById(frameId + '-frame');

            try {
                // Try to access iframe content to check for loading screen
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;

                if (iframeDoc) {
                    const loadingElement = iframeDoc.getElementById('root-app-loading-wheel') ||
                                         iframeDoc.querySelector('.loading') ||
                                         iframeDoc.querySelector('[id*="loading"]');

                    if (loadingElement && loadingElement.style.display !== 'none') {
                        updateStatus(frameId, '⚠️ Loading screen detected - refreshing...', 'loading');
                        console.log(`${frameId}: Loading screen stuck, refreshing iframe`);

                        // Refresh the iframe
                        const currentSrc = iframe.src;
                        iframe.src = 'about:blank';
                        setTimeout(() => {
                            iframe.src = currentSrc;
                        }, 1000);
                    } else {
                        updateStatus(frameId, '✅ Fully loaded! DAT interface ready!', 'success');
                    }
                }
            } catch (e) {
                // Cross-origin access blocked - assume it's working
                updateStatus(frameId, '✅ Loaded (cross-origin protected)', 'success');
            }
        }

        function forceRefresh(frameId) {
            const iframe = document.getElementById(frameId + '-frame');
            const currentSrc = iframe.src;

            updateStatus(frameId, '🔄 Force refreshing...', 'loading');
            iframe.src = 'about:blank';

            setTimeout(() => {
                iframe.src = currentSrc + '?refresh=' + Date.now();
            }, 1000);
        }

        // Check for loading issues after a delay
        function checkLoadingStatus() {
            const frames = ['dat-one', 'dat-login', 'dat-main'];
            
            frames.forEach(frameId => {
                const iframe = document.getElementById(frameId + '-frame');
                const statusEl = document.getElementById(frameId + '-status');
                
                if (iframe.src !== 'about:blank' && statusEl.textContent.includes('Loading')) {
                    // Still loading after delay - might be an issue
                    updateStatus(frameId, '⚠️ Taking longer than expected...', 'loading');
                }
            });
        }

        // Auto-load DAT Login as a test
        setTimeout(() => {
            console.log('🚀 Auto-loading DAT Login for initial test...');
            loadDatLogin();
        }, 1000);

        // Check status after delays
        setTimeout(checkLoadingStatus, 10000);
        setTimeout(checkLoadingStatus, 20000);
    </script>
</body>
</html>
