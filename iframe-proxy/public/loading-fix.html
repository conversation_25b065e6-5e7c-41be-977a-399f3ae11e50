<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Loading Screen Fix</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header {
            background: #2d2d2d;
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        iframe {
            width: 100%;
            height: 800px;
            border: 2px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .controls {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.warning {
            background: #ffc107;
            color: #000;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 DAT Loading Screen Fix</h1>
            <p>Advanced iframe with automatic loading screen detection and bypass</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="loadDatLogin()">🔐 Load DAT Login</button>
            <button class="btn" onclick="loadDatDashboard()">📊 Load DAT Dashboard</button>
            <button class="btn" onclick="loadDatSearch()">🚛 Load Search Loads</button>
            <button class="btn success" onclick="forceRefresh()">🔄 Force Refresh</button>
            <button class="btn warning" onclick="bypassLoadingScreen()">⚡ Bypass Loading Screen</button>
        </div>
        
        <div id="status" class="status loading">Ready to load DAT...</div>
        
        <iframe id="datFrame" src="about:blank" onload="handleIframeLoad()"></iframe>
    </div>

    <script>
        let currentUrl = '';
        let loadingCheckInterval = null;
        
        function updateStatus(message, type = 'loading') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function loadDatLogin() {
            currentUrl = '/dat-login';
            loadUrl(currentUrl);
        }
        
        function loadDatDashboard() {
            currentUrl = '/dat-one/dashboard';
            loadUrl(currentUrl);
        }
        
        function loadDatSearch() {
            currentUrl = '/dat-one/search-loads-ow';
            loadUrl(currentUrl);
        }
        
        function loadUrl(url) {
            updateStatus(`🔄 Loading ${url}...`, 'loading');
            document.getElementById('datFrame').src = url;
            
            // Start monitoring for loading screen
            startLoadingScreenMonitor();
        }
        
        function handleIframeLoad() {
            if (currentUrl) {
                updateStatus('✅ Page loaded! Checking for loading screen...', 'success');
                
                // Check for loading screen after a delay
                setTimeout(checkForLoadingScreen, 3000);
                setTimeout(checkForLoadingScreen, 6000);
                setTimeout(checkForLoadingScreen, 10000);
            }
        }
        
        function startLoadingScreenMonitor() {
            // Clear any existing interval
            if (loadingCheckInterval) {
                clearInterval(loadingCheckInterval);
            }
            
            // Check every 2 seconds for loading screen
            loadingCheckInterval = setInterval(checkForLoadingScreen, 2000);
            
            // Stop checking after 30 seconds
            setTimeout(() => {
                if (loadingCheckInterval) {
                    clearInterval(loadingCheckInterval);
                    loadingCheckInterval = null;
                }
            }, 30000);
        }
        
        function checkForLoadingScreen() {
            const iframe = document.getElementById('datFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                if (iframeDoc) {
                    // Look for DAT loading screen elements
                    const loadingElements = [
                        iframeDoc.getElementById('root-app-loading-wheel'),
                        iframeDoc.querySelector('.loading'),
                        iframeDoc.querySelector('[id*="loading"]'),
                        iframeDoc.querySelector('.wheel-container'),
                        iframeDoc.querySelector('.anim-container')
                    ].filter(el => el !== null);
                    
                    const visibleLoadingElements = loadingElements.filter(el => {
                        const style = window.getComputedStyle(el);
                        return style.display !== 'none' && style.visibility !== 'hidden';
                    });
                    
                    if (visibleLoadingElements.length > 0) {
                        updateStatus('⚠️ Loading screen detected! Auto-bypassing...', 'loading');
                        console.log('Loading screen detected, attempting bypass...');
                        
                        // Try to bypass the loading screen
                        setTimeout(bypassLoadingScreen, 1000);
                    } else {
                        updateStatus('✅ DAT interface fully loaded!', 'success');
                        
                        // Stop monitoring
                        if (loadingCheckInterval) {
                            clearInterval(loadingCheckInterval);
                            loadingCheckInterval = null;
                        }
                    }
                }
            } catch (e) {
                // Cross-origin access blocked - assume it's working
                updateStatus('✅ Loaded (cross-origin protected)', 'success');
                
                // Stop monitoring
                if (loadingCheckInterval) {
                    clearInterval(loadingCheckInterval);
                    loadingCheckInterval = null;
                }
            }
        }
        
        function bypassLoadingScreen() {
            const iframe = document.getElementById('datFrame');
            
            try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                
                if (iframeDoc) {
                    updateStatus('⚡ Attempting to bypass loading screen...', 'loading');
                    
                    // Method 1: Hide loading elements
                    const loadingElements = [
                        iframeDoc.getElementById('root-app-loading-wheel'),
                        iframeDoc.querySelector('.loading'),
                        iframeDoc.querySelector('[id*="loading"]')
                    ].filter(el => el !== null);
                    
                    loadingElements.forEach(el => {
                        el.style.display = 'none';
                        el.style.visibility = 'hidden';
                    });
                    
                    // Method 2: Trigger any completion events
                    const events = ['DOMContentLoaded', 'load', 'readystatechange'];
                    events.forEach(eventType => {
                        const event = new Event(eventType);
                        iframeDoc.dispatchEvent(event);
                    });
                    
                    // Method 3: Set document ready state
                    if (iframeDoc.readyState !== 'complete') {
                        Object.defineProperty(iframeDoc, 'readyState', {
                            value: 'complete',
                            writable: false
                        });
                    }
                    
                    updateStatus('✅ Loading screen bypass attempted!', 'success');
                    
                    // Check again after bypass
                    setTimeout(checkForLoadingScreen, 2000);
                } else {
                    updateStatus('❌ Cannot access iframe content (cross-origin)', 'error');
                }
            } catch (e) {
                updateStatus('❌ Cannot bypass loading screen (cross-origin)', 'error');
                console.error('Bypass error:', e);
            }
        }
        
        function forceRefresh() {
            if (currentUrl) {
                updateStatus('🔄 Force refreshing...', 'loading');
                const iframe = document.getElementById('datFrame');
                iframe.src = 'about:blank';
                
                setTimeout(() => {
                    iframe.src = currentUrl + '?refresh=' + Date.now();
                }, 1000);
            }
        }
        
        // Auto-load DAT login for testing
        setTimeout(() => {
            console.log('🚀 Auto-loading DAT Login...');
            loadDatLogin();
        }, 1000);
    </script>
</body>
</html>
