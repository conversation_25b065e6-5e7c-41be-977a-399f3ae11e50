const puppeteer = require('puppeteer');
const fs = require('fs');

async function authenticateDAT(email, password, emailCode = null) {
    console.log('🚀 Starting DAT Manual Authentication...');
    
    const browser = await puppeteer.launch({
        headless: false,
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor'
        ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

    try {
        // Step 1: Navigate to DAT login
        console.log('📍 Navigating to DAT login...');
        await page.goto('https://login.dat.com', { waitUntil: 'networkidle2' });
        
        // Take screenshot of login page
        await page.screenshot({ path: 'step1-login-page.png', fullPage: true });
        console.log('📸 Screenshot saved: step1-login-page.png');

        // Step 2: Enter credentials
        console.log('📝 Entering credentials...');
        
        // Wait for email field and enter email
        await page.waitForSelector('input[type="email"], input[name="username"]', { timeout: 10000 });
        const emailField = await page.$('input[type="email"], input[name="username"]');
        await emailField.click();
        await emailField.type(email);
        
        // Enter password
        const passwordField = await page.$('input[type="password"]');
        await passwordField.click();
        await passwordField.type(password);
        
        // Take screenshot after entering credentials
        await page.screenshot({ path: 'step2-credentials-entered.png', fullPage: true });
        console.log('📸 Screenshot saved: step2-credentials-entered.png');
        
        // Click login button
        const loginButton = await page.$('button[type="submit"], input[type="submit"]');
        await loginButton.click();
        console.log('✅ Login button clicked');
        
        // Wait for response
        await page.waitForTimeout(5000);
        
        // Take screenshot after login attempt
        await page.screenshot({ path: 'step3-after-login.png', fullPage: true });
        console.log('📸 Screenshot saved: step3-after-login.png');

        // Step 3: Handle 2FA if needed
        if (emailCode) {
            console.log('🔐 Handling 2FA...');
            
            try {
                // Look for 2FA code input
                await page.waitForSelector('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]', { timeout: 10000 });
                const codeField = await page.$('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"]');
                
                if (codeField) {
                    await codeField.click();
                    await codeField.type(emailCode);
                    
                    // Click verify button
                    const verifyButton = await page.$('button[type="submit"]');
                    if (verifyButton) {
                        await verifyButton.click();
                        console.log('✅ 2FA code submitted');
                    }
                    
                    await page.waitForTimeout(5000);
                    
                    // Take screenshot after 2FA
                    await page.screenshot({ path: 'step4-after-2fa.png', fullPage: true });
                    console.log('📸 Screenshot saved: step4-after-2fa.png');
                }
            } catch (error) {
                console.log('⚠️ No 2FA prompt found or already passed');
            }
        }

        // Step 4: Wait for dashboard and extract session
        console.log('⏳ Waiting for dashboard...');
        
        // Wait for navigation to complete
        await page.waitForTimeout(10000);
        
        const currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);
        
        // Take final screenshot
        await page.screenshot({ path: 'step5-final-page.png', fullPage: true });
        console.log('📸 Screenshot saved: step5-final-page.png');

        // Extract session data
        console.log('📊 Extracting session data...');
        
        const cookies = await page.cookies();
        const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        
        const storageData = await page.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href,
                title: document.title
            };
        });
        
        const sessionData = {
            cookies: cookieString,
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            title: storageData.title,
            timestamp: new Date().toISOString(),
            cookieCount: cookies.length
        };
        
        // Save session data
        fs.writeFileSync('dat-session.json', JSON.stringify(sessionData, null, 2));
        console.log('✅ Session data saved to dat-session.json');
        
        // Print summary
        console.log('\n🎉 Authentication Summary:');
        console.log(`📍 Final URL: ${sessionData.currentUrl}`);
        console.log(`📄 Page Title: ${sessionData.title}`);
        console.log(`🍪 Cookies: ${sessionData.cookieCount}`);
        console.log(`📊 LocalStorage: ${sessionData.localStorage.length} chars`);
        
        // Check if we're on DAT One
        if (currentUrl.includes('one.dat.com') || sessionData.title.includes('DAT')) {
            console.log('✅ Successfully authenticated to DAT!');
            return sessionData;
        } else {
            console.log('⚠️ May not be fully authenticated. Check screenshots.');
            return sessionData;
        }
        
    } catch (error) {
        console.error('❌ Authentication error:', error.message);
        
        // Take error screenshot
        await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
        console.log('📸 Error screenshot saved: error-screenshot.png');
        
        throw error;
    } finally {
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser kept open for manual inspection.');
        console.log('Close the browser window when you\'re done inspecting.');
        
        // Don't close browser automatically
        // await browser.close();
    }
}

// Export for use
module.exports = { authenticateDAT };

// If run directly with command line arguments
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.log('Usage: node manual-auth.js <email> <password> [emailCode]');
        console.log('Example: node manual-auth.js <EMAIL> mypassword 123456');
        process.exit(1);
    }
    
    const [email, password, emailCode] = args;
    
    authenticateDAT(email, password, emailCode)
        .then(sessionData => {
            console.log('\n✅ Authentication completed successfully!');
            console.log('Session data saved and ready for proxy use.');
        })
        .catch(error => {
            console.error('\n❌ Authentication failed:', error.message);
            process.exit(1);
        });
}
