const puppeteer = require('puppeteer');
const fs = require('fs');

async function authenticateDAT(email, password, emailCode = null) {
    console.log('🚀 Starting DAT Manual Authentication...');
    
    const browser = await puppeteer.launch({
        headless: true, // Run in headless mode for server
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-gpu',
            '--disable-dev-shm-usage',
            '--disable-extensions',
            '--no-first-run',
            '--no-zygote',
            '--single-process'
        ]
    });

    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');

    try {
        // Step 1: Navigate to DAT dashboard (will redirect to login)
        console.log('📍 Navigating to DAT dashboard (will redirect to login)...');
        await page.goto('https://one.dat.com/dashboard', { waitUntil: 'networkidle2' });
        
        // Take screenshot of login page
        await page.screenshot({ path: 'step1-login-page.png', fullPage: true });
        console.log('📸 Screenshot saved: step1-login-page.png');

        // Step 2: Enter credentials
        console.log('📝 Entering credentials...');

        // Wait for email field and enter email
        await page.waitForSelector('input[type="email"], input[name="username"], input[name="email"]', { timeout: 10000 });

        // Try multiple selectors for email field
        const emailSelectors = ['input[type="email"]', 'input[name="username"]', 'input[name="email"]', 'input[placeholder*="email"]'];
        let emailField = null;

        for (const selector of emailSelectors) {
            try {
                emailField = await page.$(selector);
                if (emailField) {
                    console.log(`✅ Found email field with selector: ${selector}`);
                    break;
                }
            } catch (e) {}
        }

        if (!emailField) {
            throw new Error('❌ Could not find email input field');
        }

        await emailField.click();
        await new Promise(resolve => setTimeout(resolve, 500));
        await emailField.type(email, { delay: 100 });
        console.log('✅ Email entered');

        // Enter password
        await new Promise(resolve => setTimeout(resolve, 1000));
        const passwordField = await page.$('input[type="password"]');
        if (!passwordField) {
            throw new Error('❌ Could not find password input field');
        }

        await passwordField.click();
        await new Promise(resolve => setTimeout(resolve, 500));
        await passwordField.type(password, { delay: 100 });
        console.log('✅ Password entered');
        
        // Take screenshot after entering credentials
        await page.screenshot({ path: 'step2-credentials-entered.png', fullPage: true });
        console.log('📸 Screenshot saved: step2-credentials-entered.png');

        // Click login button
        await new Promise(resolve => setTimeout(resolve, 1000));
        const loginButtonSelectors = [
            'button[type="submit"]',
            'input[type="submit"]',
            'button:contains("Log in")',
            'button:contains("Sign in")',
            'button:contains("Continue")',
            '.login-button',
            '[data-testid*="login"]'
        ];

        let loginButton = null;
        for (const selector of loginButtonSelectors) {
            try {
                loginButton = await page.$(selector);
                if (loginButton) {
                    console.log(`✅ Found login button with selector: ${selector}`);
                    break;
                }
            } catch (e) {}
        }

        // If no button found by selector, try finding by text
        if (!loginButton) {
            const buttons = await page.$$('button, input[type="submit"]');
            for (const button of buttons) {
                const text = await page.evaluate(el => el.textContent || el.value, button);
                if (text && (text.toLowerCase().includes('log in') || text.toLowerCase().includes('sign in') || text.toLowerCase().includes('continue'))) {
                    loginButton = button;
                    console.log(`✅ Found login button by text: "${text}"`);
                    break;
                }
            }
        }

        if (!loginButton) {
            throw new Error('❌ Could not find login button');
        }

        await loginButton.click();
        console.log('✅ Login button clicked');
        
        // Wait for response
        await new Promise(resolve => setTimeout(resolve, 5000));
        
        // Take screenshot after login attempt
        await page.screenshot({ path: 'step3-after-login.png', fullPage: true });
        console.log('📸 Screenshot saved: step3-after-login.png');

        // Step 3: Handle 2FA - Look for "Try another method" and select email
        console.log('🔐 Looking for 2FA options...');

        try {
            // Wait for 2FA page to load
            await new Promise(resolve => setTimeout(resolve, 3000));

            // Look for "Try another method" button/link
            const tryAnotherMethodSelectors = [
                'button:contains("Try another method")',
                'a:contains("Try another method")',
                '[data-testid*="try-another"]',
                'button[value="pick-authenticator"]',
                '.try-another-method'
            ];

            let tryAnotherButton = null;
            for (const selector of tryAnotherMethodSelectors) {
                try {
                    await page.waitForSelector(selector, { timeout: 3000 });
                    tryAnotherButton = await page.$(selector);
                    if (tryAnotherButton) {
                        console.log(`✅ Found "Try another method" with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    // Try text-based search
                    const elements = await page.$$('button, a, span');
                    for (const element of elements) {
                        const text = await page.evaluate(el => el.textContent, element);
                        if (text && text.toLowerCase().includes('try another method')) {
                            tryAnotherButton = element;
                            console.log('✅ Found "Try another method" by text content');
                            break;
                        }
                    }
                    if (tryAnotherButton) break;
                }
            }

            if (tryAnotherButton) {
                await tryAnotherButton.click();
                console.log('✅ Clicked "Try another method"');
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Take screenshot after clicking try another method
                await page.screenshot({ path: 'step4a-try-another-method.png', fullPage: true });
                console.log('📸 Screenshot saved: step4a-try-another-method.png');

                // Look for email option
                const emailOptionSelectors = [
                    'button:contains("Email")',
                    'a:contains("Email")',
                    '[data-testid*="email"]',
                    'button[value*="email"]'
                ];

                let emailOption = null;
                for (const selector of emailOptionSelectors) {
                    try {
                        await page.waitForSelector(selector, { timeout: 3000 });
                        emailOption = await page.$(selector);
                        if (emailOption) {
                            console.log(`✅ Found email option with selector: ${selector}`);
                            break;
                        }
                    } catch (e) {
                        // Try text-based search
                        const elements = await page.$$('button, a, span, div');
                        for (const element of elements) {
                            const text = await page.evaluate(el => el.textContent, element);
                            if (text && text.toLowerCase().includes('email')) {
                                emailOption = element;
                                console.log('✅ Found email option by text content');
                                break;
                            }
                        }
                        if (emailOption) break;
                    }
                }

                if (emailOption) {
                    await emailOption.click();
                    console.log('✅ Selected email verification method');
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // Take screenshot after selecting email
                    await page.screenshot({ path: 'step4b-email-selected.png', fullPage: true });
                    console.log('📸 Screenshot saved: step4b-email-selected.png');
                    console.log('📧 Email verification code should be sent now!');
                    console.log('⏳ Waiting for email code input field...');
                }
            }

            // Now handle the email code if provided
            if (emailCode) {
                console.log('🔐 Entering email verification code...');

                // Look for email code input field
                await page.waitForSelector('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"], input[type="number"]', { timeout: 15000 });
                const codeField = await page.$('input[name="code"], input[placeholder*="code"], input[type="text"][maxlength="6"], input[type="number"]');

                if (codeField) {
                    await codeField.click();
                    await codeField.type(emailCode);
                    console.log('✅ Email code entered');

                    // Click verify/continue button
                    const verifyButton = await page.$('button[type="submit"], button:contains("Verify"), button:contains("Continue")');
                    if (verifyButton) {
                        await verifyButton.click();
                        console.log('✅ Verify button clicked');
                    }

                    await new Promise(resolve => setTimeout(resolve, 5000));

                    // Take screenshot after 2FA
                    await page.screenshot({ path: 'step4c-after-email-verification.png', fullPage: true });
                    console.log('📸 Screenshot saved: step4c-after-email-verification.png');
                }
            } else {
                console.log('⏳ No email code provided yet. Script will continue to wait...');
                console.log('📧 Please check your email and provide the verification code.');

                // Take screenshot showing we're waiting for code
                await page.screenshot({ path: 'step4-waiting-for-email-code.png', fullPage: true });
                console.log('📸 Screenshot saved: step4-waiting-for-email-code.png');

                // Return early so user can provide the code
                console.log('🔄 Please run the script again with the email code as the third parameter.');
                return null;
            }

        } catch (error) {
            console.log('⚠️ 2FA handling error:', error.message);
            await page.screenshot({ path: 'step4-2fa-error.png', fullPage: true });
            console.log('📸 Screenshot saved: step4-2fa-error.png');
        }

        // Step 4: Wait for dashboard and extract session
        console.log('⏳ Waiting for dashboard...');
        
        // Wait for navigation to complete
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        const currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);
        
        // Take final screenshot
        await page.screenshot({ path: 'step5-final-page.png', fullPage: true });
        console.log('📸 Screenshot saved: step5-final-page.png');

        // Extract session data
        console.log('📊 Extracting session data...');
        
        const cookies = await page.cookies();
        const cookieString = cookies.map(c => `${c.name}=${c.value}`).join('; ');
        
        const storageData = await page.evaluate(() => {
            return {
                localStorage: JSON.stringify(localStorage),
                sessionStorage: JSON.stringify(sessionStorage),
                currentUrl: window.location.href,
                title: document.title
            };
        });
        
        const sessionData = {
            cookies: cookieString,
            localStorage: storageData.localStorage,
            sessionStorage: storageData.sessionStorage,
            currentUrl: storageData.currentUrl,
            title: storageData.title,
            timestamp: new Date().toISOString(),
            cookieCount: cookies.length
        };
        
        // Save session data
        fs.writeFileSync('dat-session.json', JSON.stringify(sessionData, null, 2));
        console.log('✅ Session data saved to dat-session.json');
        
        // Print summary
        console.log('\n🎉 Authentication Summary:');
        console.log(`📍 Final URL: ${sessionData.currentUrl}`);
        console.log(`📄 Page Title: ${sessionData.title}`);
        console.log(`🍪 Cookies: ${sessionData.cookieCount}`);
        console.log(`📊 LocalStorage: ${sessionData.localStorage.length} chars`);
        
        // Check if we're on DAT One
        if (currentUrl.includes('one.dat.com') || sessionData.title.includes('DAT')) {
            console.log('✅ Successfully authenticated to DAT!');
            return sessionData;
        } else {
            console.log('⚠️ May not be fully authenticated. Check screenshots.');
            return sessionData;
        }
        
    } catch (error) {
        console.error('❌ Authentication error:', error.message);
        
        // Take error screenshot
        await page.screenshot({ path: 'error-screenshot.png', fullPage: true });
        console.log('📸 Error screenshot saved: error-screenshot.png');
        
        throw error;
    } finally {
        // Keep browser open for manual inspection
        console.log('\n🔍 Browser kept open for manual inspection.');
        console.log('Close the browser window when you\'re done inspecting.');
        
        // Don't close browser automatically
        // await browser.close();
    }
}

// Export for use
module.exports = { authenticateDAT };

// If run directly with command line arguments
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length < 2) {
        console.log('Usage: node manual-auth.js <email> <password> [emailCode]');
        console.log('Example: node manual-auth.js <EMAIL> mypassword 123456');
        process.exit(1);
    }
    
    const [email, password, emailCode] = args;
    
    authenticateDAT(email, password, emailCode)
        .then(sessionData => {
            console.log('\n✅ Authentication completed successfully!');
            console.log('Session data saved and ready for proxy use.');
        })
        .catch(error => {
            console.error('\n❌ Authentication failed:', error.message);
            process.exit(1);
        });
}
