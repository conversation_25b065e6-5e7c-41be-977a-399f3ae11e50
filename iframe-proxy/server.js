const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');
const fetch = require('node-fetch');

const app = express();
const PORT = 3004;

// Enable CORS for all routes
app.use(cors());

// Direct authenticated DAT dashboard endpoint
app.get('/dat-authenticated', (req, res) => {
    const manualSessionId = loadManualAuthSession();

    if (manualSessionId) {
        const authenticatedUrl = `http://${req.get('host')}/authenticated/${manualSessionId}/dat-one/dashboard`;
        res.redirect(authenticatedUrl);
    } else {
        res.status(500).send('Manual authentication session not available');
    }
});

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// URL rewriting map for DAT subdomains
const urlRewriteMap = {
    'https://shared-content-cargo.dat.com': '/shared-content-cargo',
    'https://shared-content.prod.dat.com': '/shared-content',
    'https://one.prod-my-shipments.prod.dat.com': '/my-shipments',
    'https://one.prod-private-network.prod.dat.com': '/private-network',
    'https://one.freight-search-prod.prod.dat.com': '/freight-search',
    'https://one.company-search-prod.prod.dat.com': '/company-search',
    'https://one.dashboard.dat.com': '/dashboard-assets',
    'https://freight.api.dat.com': '/freight-api',
    'https://identity.api.dat.com': '/identity-api',
    'https://js.api.here.com': '/here-maps',
    'https://maps.googleapis.com': '/google-maps',
    'https://one.dat.com': '/dat-one',
    'https://login.dat.com': '/dat-login',
    'https://www.dat.com': '/dat-main'
};

// Function to rewrite URLs in HTML content
function rewriteURLsInContent(content, contentType) {
    if (!contentType || !contentType.includes('text/html')) {
        return content;
    }

    let rewrittenContent = content;

    // Rewrite absolute URLs to use our proxy
    for (const [originalUrl, proxyPath] of Object.entries(urlRewriteMap)) {
        const regex = new RegExp(originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        rewrittenContent = rewrittenContent.replace(regex, proxyPath);
    }

    return rewrittenContent;
}

// Custom middleware to remove X-Frame-Options and CSP headers
const removeFrameBlocking = (proxyRes, req, res) => {
    // Remove headers that block iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['content-security-policy-report-only'];

    // Add headers to allow iframe embedding and fix CORS
    proxyRes.headers['x-frame-options'] = 'ALLOWALL';
    proxyRes.headers['access-control-allow-origin'] = '*';
    proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['access-control-allow-headers'] = '*';

    // Fix caching issues that might prevent JS/CSS loading
    if (req.originalUrl.includes('.js') || req.originalUrl.includes('.css')) {
        proxyRes.headers['cache-control'] = 'no-cache, no-store, must-revalidate';
        proxyRes.headers['pragma'] = 'no-cache';
        proxyRes.headers['expires'] = '0';
    }

    console.log(`📡 Proxied: ${req.method} ${req.originalUrl} -> Status: ${proxyRes.statusCode} (${proxyRes.headers['content-type'] || 'unknown'})`);
};

// Proxy configuration for DAT One
const datOneProxy = createProxyMiddleware({
    target: 'https://one.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-one': '', // Remove /dat-one prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT One Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Login
const datLoginProxy = createProxyMiddleware({
    target: 'https://login.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-login': '', // Remove /dat-login prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT Login Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Main Site
const datMainProxy = createProxyMiddleware({
    target: 'https://www.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-main': '', // Remove /dat-main prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT Main Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Comprehensive proxy for all DAT subdomains
const createDATProxy = (target, pathPrefix) => createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        [`^${pathPrefix}`]: '', // Remove prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error(`❌ Proxy Error for ${pathPrefix}:`, err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Middleware to handle authentication bypass and missing files
app.use((req, res, next) => {
    // Handle callback URL mismatch by redirecting to a working page
    if (req.path === '/callback') {
        console.log(`🔄 Intercepting callback redirect, sending to DAT main page`);
        return res.redirect('/dat-main/');
    }

    // Check if this is a JS/CSS file request that should be under /dat-one/
    if ((req.path.endsWith('.js') || req.path.endsWith('.css')) && !req.path.startsWith('/dat-')) {
        console.log(`🔄 Redirecting ${req.path} to /dat-one${req.path}`);
        return res.redirect(`/dat-one${req.path}`);
    }

    next();
});

// Apply comprehensive proxy middleware
app.use('/dat-one', datOneProxy);
app.use('/dat-login', datLoginProxy);
app.use('/dat-main', datMainProxy);

// Additional DAT subdomains
app.use('/shared-content-cargo', createDATProxy('https://shared-content-cargo.dat.com', '/shared-content-cargo'));
app.use('/shared-content', createDATProxy('https://shared-content.prod.dat.com', '/shared-content'));
app.use('/my-shipments', createDATProxy('https://one.prod-my-shipments.prod.dat.com', '/my-shipments'));
app.use('/private-network', createDATProxy('https://one.prod-private-network.prod.dat.com', '/private-network'));
app.use('/freight-search', createDATProxy('https://one.freight-search-prod.prod.dat.com', '/freight-search'));
app.use('/company-search', createDATProxy('https://one.company-search-prod.prod.dat.com', '/company-search'));
app.use('/dashboard-assets', createDATProxy('https://one.dashboard.dat.com', '/dashboard-assets'));
app.use('/freight-api', createDATProxy('https://freight.api.dat.com', '/freight-api'));
app.use('/identity-api', createDATProxy('https://identity.api.dat.com', '/identity-api'));

// External services (optional - may have CORS issues)
app.use('/here-maps', createDATProxy('https://js.api.here.com', '/here-maps'));
app.use('/google-maps', createDATProxy('https://maps.googleapis.com', '/google-maps'));

// Special route for demo/test access without authentication
app.get('/dat-demo', (req, res) => {
    res.send(`
        <html>
        <head>
            <title>DAT Demo Access</title>
            <style>
                body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
                .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
                .btn { background: #0066cc; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 10px; cursor: pointer; }
                iframe { width: 100%; height: 600px; border: 1px solid #ddd; margin-top: 20px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 DAT Demo Access</h1>
                <p>This page provides demo access to DAT services without authentication requirements.</p>

                <button class="btn" onclick="loadDemo('main')">Load DAT Main Site</button>
                <button class="btn" onclick="loadDemo('public')">Load DAT Public Pages</button>
                <button class="btn" onclick="loadDemo('info')">Load DAT Info</button>

                <iframe id="demoFrame" src="/dat-main/"></iframe>

                <script>
                    function loadDemo(type) {
                        const frame = document.getElementById('demoFrame');
                        switch(type) {
                            case 'main':
                                frame.src = '/dat-main/';
                                break;
                            case 'public':
                                frame.src = '/dat-main/solutions';
                                break;
                            case 'info':
                                frame.src = '/dat-main/about';
                                break;
                        }
                    }
                </script>
            </div>
        </body>
        </html>
    `);
});

// Store authenticated sessions
const authenticatedSessions = new Map();

// Load the manually authenticated session data
function loadManualAuthSession() {
    try {
        const fs = require('fs');
        const sessionData = JSON.parse(fs.readFileSync('dat-session.json', 'utf8'));

        console.log('🔐 Loading manually authenticated DAT session...');
        console.log(`📍 Session URL: ${sessionData.currentUrl}`);
        console.log(`🍪 Cookies: ${sessionData.cookieCount}`);
        console.log(`📊 LocalStorage: ${sessionData.localStorage.length} chars`);

        // Create a special session ID for the manual auth
        const manualSessionId = 'manual-auth-session';
        authenticatedSessions.set(manualSessionId, sessionData);

        console.log(`✅ Manual auth session loaded with ID: ${manualSessionId}`);
        return manualSessionId;

    } catch (error) {
        console.error('❌ Failed to load manual auth session:', error.message);
        return null;
    }
}

// API endpoint to use manual authenticated session
app.get('/api/use-manual-auth', (req, res) => {
    const manualSessionId = loadManualAuthSession();

    if (manualSessionId) {
        const proxyUrl = `http://${req.get('host')}/authenticated/${manualSessionId}`;
        res.json({
            success: true,
            sessionId: manualSessionId,
            proxyUrl: proxyUrl,
            message: 'Manual authentication session loaded successfully'
        });
    } else {
        res.status(500).json({
            success: false,
            error: 'Failed to load manual authentication session'
        });
    }
});

// API endpoint to create authenticated session
app.post('/api/create-authenticated-session', express.json(), (req, res) => {
    try {
        const { cookies, localStorage, sessionStorage, authHeader, currentUrl } = req.body;

        if (!cookies && !authHeader) {
            return res.json({ success: false, error: 'No authentication data provided' });
        }

        // Generate session ID
        const sessionId = require('crypto').randomUUID();

        // Store session data
        authenticatedSessions.set(sessionId, {
            cookies: cookies,
            localStorage: localStorage,
            sessionStorage: sessionStorage,
            authHeader: authHeader,
            currentUrl: currentUrl,
            created: new Date().toISOString()
        });

        console.log(`🔐 Created authenticated session: ${sessionId}`);

        res.json({
            success: true,
            sessionId: sessionId,
            proxyUrl: `http://*************:3004/authenticated/${sessionId}`
        });

    } catch (error) {
        console.error('❌ Error creating authenticated session:', error);
        res.json({ success: false, error: error.message });
    }
});

// API endpoint to test session validity
app.post('/api/test-session', express.json(), (req, res) => {
    try {
        const { cookies, authHeader } = req.body;

        // Basic validation
        if (!cookies && !authHeader) {
            return res.json({ valid: false, reason: 'No authentication data provided' });
        }

        // Check for common DAT session indicators
        const hasValidCookies = cookies && (
            cookies.includes('auth0') ||
            cookies.includes('session') ||
            cookies.includes('token') ||
            cookies.includes('dat')
        );

        const hasValidAuth = authHeader && (
            authHeader.startsWith('Bearer ') ||
            authHeader.includes('token')
        );

        if (hasValidCookies || hasValidAuth) {
            res.json({ valid: true, reason: 'Session data looks valid' });
        } else {
            res.json({ valid: false, reason: 'No recognizable session tokens found' });
        }

    } catch (error) {
        console.error('❌ Error testing session:', error);
        res.json({ valid: false, reason: error.message });
    }
});

// Create DAT session and get Bearer token
async function createDATSession(sessionData) {
    try {
        const response = await fetch('https://identity.api.dat.com/usurp/v1/session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Cookie': sessionData.cookies,
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            },
            body: JSON.stringify({
                userId: 'auth0|64355ce3d8f24d23556f11f9',
                appId: 'DAT One Web',
                deviceType: 'desktop',
                agentDeviceId: '15746b34-803e-1bb3-5be6-960a073fc692'
            })
        });

        if (response.ok) {
            const data = await response.json();
            console.log(`🔐 Created DAT session: ${data.sessionId}`);
            return data.sessionId;
        } else {
            console.error('❌ Failed to create DAT session:', response.status);
            return null;
        }
    } catch (error) {
        console.error('❌ Error creating DAT session:', error);
        return null;
    }
}

// Create authenticated proxy middleware
const createAuthenticatedProxy = (sessionData, target) => createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    onProxyReq: async (proxyReq, req, res) => {
        // Add the user's cookies to the request
        if (sessionData.cookies) {
            proxyReq.setHeader('Cookie', sessionData.cookies);
        }

        // For API requests, add Authorization header with session token
        if (req.originalUrl.includes('api.dat.com')) {
            if (!sessionData.bearerToken) {
                sessionData.bearerToken = await createDATSession(sessionData);
            }

            if (sessionData.bearerToken) {
                proxyReq.setHeader('Authorization', `Bearer ${sessionData.bearerToken}`);
                console.log(`🔐 Added Bearer token to API request: ${req.originalUrl}`);
            }
        }

        // Add proper headers
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');

        console.log(`🔐 Authenticated request: ${req.method} ${req.originalUrl}`);
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error(`❌ Authenticated Proxy Error:`, err.message);
        res.status(500).send('Authenticated Proxy Error: ' + err.message);
    }
});

// Authenticated proxy routes
app.use('/authenticated/:sessionId/dat-one', (req, res, next) => {
    const sessionId = req.params.sessionId;
    const sessionData = authenticatedSessions.get(sessionId);

    if (!sessionData) {
        return res.status(404).send('Session not found or expired');
    }

    // Create authenticated proxy for this session
    const authProxy = createAuthenticatedProxy(sessionData, 'https://one.dat.com');

    // Remove the session ID from the path
    req.url = req.url.replace(`/authenticated/${sessionId}/dat-one`, '');
    if (!req.url.startsWith('/')) req.url = '/' + req.url;

    authProxy(req, res, next);
});

app.use('/authenticated/:sessionId/dat-login', (req, res, next) => {
    const sessionId = req.params.sessionId;
    const sessionData = authenticatedSessions.get(sessionId);

    if (!sessionData) {
        return res.status(404).send('Session not found or expired');
    }

    const authProxy = createAuthenticatedProxy(sessionData, 'https://login.dat.com');
    req.url = req.url.replace(`/authenticated/${sessionId}/dat-login`, '');
    if (!req.url.startsWith('/')) req.url = '/' + req.url;

    authProxy(req, res, next);
});

// Main authenticated proxy route (catch-all)
app.get('/authenticated/:sessionId', (req, res) => {
    const sessionId = req.params.sessionId;

    const sessionData = authenticatedSessions.get(sessionId);
    if (!sessionData) {
        return res.status(404).send(`
            <html>
            <head><title>Session Not Found</title></head>
            <body>
                <h1>❌ Session Not Found</h1>
                <p>Session ID: <code>${sessionId}</code></p>
                <p>The session may have expired or the server was restarted.</p>
                <p><a href="/session-capture.html">Create New Session</a></p>
            </body>
            </html>
        `);
    }

    console.log(`🔐 Using authenticated session ${sessionId} for main page`);

    // Show authenticated dashboard
    res.send(`
        <html>
        <head>
            <title>🔐 Authenticated DAT Access</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                .header { background: #2d2d2d; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
                .btn { background: #0066cc; color: white; padding: 10px 20px; border: none; border-radius: 4px; margin: 5px; cursor: pointer; text-decoration: none; display: inline-block; }
                iframe { width: 100%; height: 800px; border: 2px solid #ddd; border-radius: 4px; margin: 10px 0; }
                .info { background: #d4edda; padding: 15px; border-radius: 4px; margin: 15px 0; color: #155724; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔐 Authenticated DAT Access</h1>
                    <p>Session ID: <code>${sessionId}</code></p>
                    <p>User: <strong>Hakob Ter-Sahakyan (Reize LLC)</strong></p>
                </div>

                <div class="info">
                    <h4>✅ Authentication Status: ACTIVE</h4>
                    <p>Your DAT session has been successfully captured and is being used for authenticated access.</p>
                </div>

                <div>
                    <a href="/authenticated/${sessionId}/dat-one/dashboard" class="btn">📊 Load DAT Dashboard</a>
                    <a href="/authenticated/${sessionId}/dat-one/search-loads-ow" class="btn">🚛 Search Loads</a>
                    <a href="/authenticated/${sessionId}/dat-one/" class="btn">🏠 DAT Home</a>
                </div>

                <iframe src="/authenticated/${sessionId}/dat-one/dashboard" onload="console.log('Authenticated DAT loaded!')"></iframe>
            </div>
        </body>
        </html>
    `);
});

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        proxies: {
            'dat-one': 'https://one.dat.com',
            'dat-login': 'https://login.dat.com',
            'dat-main': 'https://www.dat.com'
        },
        authBypass: 'Available at /dat-demo',
        sessionCapture: 'Available at /session-capture.html',
        authenticatedSessions: authenticatedSessions.size
    });
});

// API endpoint to test proxy connectivity
app.get('/api/test-proxy/:target', async (req, res) => {
    const { target } = req.params;
    const targetUrls = {
        'dat-one': 'https://one.dat.com',
        'dat-login': 'https://login.dat.com',
        'dat-main': 'https://www.dat.com'
    };
    
    const targetUrl = targetUrls[target];
    if (!targetUrl) {
        return res.status(400).json({ error: 'Invalid target' });
    }
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(targetUrl, {
            method: 'HEAD',
            timeout: 10000
        });
        
        res.json({
            target: target,
            url: targetUrl,
            status: response.status,
            accessible: response.ok,
            headers: Object.fromEntries(response.headers.entries())
        });
    } catch (error) {
        res.json({
            target: target,
            url: targetUrl,
            accessible: false,
            error: error.message
        });
    }
});

// Default route
app.get('/', (req, res) => {
    res.send(`
        <h1>🚀 DAT Comprehensive iframe Proxy Server</h1>
        <p>Reverse proxy to bypass X-Frame-Options for ALL DAT websites and subdomains</p>
        <h2>Main DAT Proxies:</h2>
        <ul>
            <li><a href="/dat-one/dashboard" target="_blank">DAT One Dashboard</a> → <code>/dat-one/*</code></li>
            <li><a href="/dat-login" target="_blank">DAT Login</a> → <code>/dat-login/*</code></li>
            <li><a href="/dat-main" target="_blank">DAT Main Site</a> → <code>/dat-main/*</code></li>
        </ul>
        <h2>DAT Subdomain Proxies:</h2>
        <ul>
            <li><strong>Assets:</strong> <code>/shared-content-cargo/*</code> → shared-content-cargo.dat.com</li>
            <li><strong>Shared Content:</strong> <code>/shared-content/*</code> → shared-content.prod.dat.com</li>
            <li><strong>My Shipments:</strong> <code>/my-shipments/*</code> → one.prod-my-shipments.prod.dat.com</li>
            <li><strong>Private Network:</strong> <code>/private-network/*</code> → one.prod-private-network.prod.dat.com</li>
            <li><strong>Freight Search:</strong> <code>/freight-search/*</code> → one.freight-search-prod.prod.dat.com</li>
            <li><strong>Company Search:</strong> <code>/company-search/*</code> → one.company-search-prod.prod.dat.com</li>
            <li><strong>Dashboard Assets:</strong> <code>/dashboard-assets/*</code> → one.dashboard.dat.com</li>
        </ul>
        <h2>DAT API Proxies:</h2>
        <ul>
            <li><strong>Freight API:</strong> <code>/freight-api/*</code> → freight.api.dat.com</li>
            <li><strong>Identity API:</strong> <code>/identity-api/*</code> → identity.api.dat.com</li>
        </ul>
        <h2>External Service Proxies:</h2>
        <ul>
            <li><strong>HERE Maps:</strong> <code>/here-maps/*</code> → js.api.here.com</li>
            <li><strong>Google Maps:</strong> <code>/google-maps/*</code> → maps.googleapis.com</li>
        </ul>
        <h2>Test Pages:</h2>
        <ul>
            <li><a href="/iframe-test.html">iframe Embedding Test</a></li>
            <li><a href="/loading-fix.html">Loading Screen Fix Test</a></li>
            <li><a href="/session-capture.html">🔐 Session Capture Tool</a></li>
            <li><a href="/dat-demo">Demo Access (No Auth)</a></li>
            <li><a href="/health">Health Check</a></li>
        </ul>
        <p><strong>Usage:</strong> Use these proxy URLs in your iframes to bypass X-Frame-Options restrictions.</p>
        <p><strong>Note:</strong> This comprehensive proxy handles ALL DAT subdomains and micro-frontends!</p>
    `);
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 DAT Comprehensive iframe Proxy Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`📡 Proxying ALL DAT subdomains:`);
    console.log(`   /dat-one/* → https://one.dat.com/*`);
    console.log(`   /dat-login/* → https://login.dat.com/*`);
    console.log(`   /dat-main/* → https://www.dat.com/*`);
    console.log(`   /shared-content-cargo/* → https://shared-content-cargo.dat.com/*`);
    console.log(`   /shared-content/* → https://shared-content.prod.dat.com/*`);
    console.log(`   /my-shipments/* → https://one.prod-my-shipments.prod.dat.com/*`);
    console.log(`   /private-network/* → https://one.prod-private-network.prod.dat.com/*`);
    console.log(`   /freight-search/* → https://one.freight-search-prod.prod.dat.com/*`);
    console.log(`   /company-search/* → https://one.company-search-prod.prod.dat.com/*`);
    console.log(`   /dashboard-assets/* → https://one.dashboard.dat.com/*`);
    console.log(`   /freight-api/* → https://freight.api.dat.com/*`);
    console.log(`   /identity-api/* → https://identity.api.dat.com/*`);
    console.log(`   /here-maps/* → https://js.api.here.com/*`);
    console.log(`   /google-maps/* → https://maps.googleapis.com/*`);
    console.log(`🎯 Ready to handle ALL DAT micro-frontends and fix loading screen!`);
});
