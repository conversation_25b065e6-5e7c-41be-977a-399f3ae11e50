const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3004;

// Enable CORS for all routes
app.use(cors());

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// URL rewriting map for DAT subdomains
const urlRewriteMap = {
    'https://shared-content-cargo.dat.com': '/shared-content-cargo',
    'https://shared-content.prod.dat.com': '/shared-content',
    'https://one.prod-my-shipments.prod.dat.com': '/my-shipments',
    'https://one.prod-private-network.prod.dat.com': '/private-network',
    'https://one.freight-search-prod.prod.dat.com': '/freight-search',
    'https://one.company-search-prod.prod.dat.com': '/company-search',
    'https://one.dashboard.dat.com': '/dashboard-assets',
    'https://freight.api.dat.com': '/freight-api',
    'https://identity.api.dat.com': '/identity-api',
    'https://js.api.here.com': '/here-maps',
    'https://maps.googleapis.com': '/google-maps',
    'https://one.dat.com': '/dat-one',
    'https://login.dat.com': '/dat-login',
    'https://www.dat.com': '/dat-main'
};

// Function to rewrite URLs in HTML content
function rewriteURLsInContent(content, contentType) {
    if (!contentType || !contentType.includes('text/html')) {
        return content;
    }

    let rewrittenContent = content;

    // Rewrite absolute URLs to use our proxy
    for (const [originalUrl, proxyPath] of Object.entries(urlRewriteMap)) {
        const regex = new RegExp(originalUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
        rewrittenContent = rewrittenContent.replace(regex, proxyPath);
    }

    return rewrittenContent;
}

// Custom middleware to remove X-Frame-Options and CSP headers
const removeFrameBlocking = (proxyRes, req, res) => {
    // Remove headers that block iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['content-security-policy-report-only'];

    // Add headers to allow iframe embedding and fix CORS
    proxyRes.headers['x-frame-options'] = 'ALLOWALL';
    proxyRes.headers['access-control-allow-origin'] = '*';
    proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
    proxyRes.headers['access-control-allow-headers'] = '*';

    // Fix caching issues that might prevent JS/CSS loading
    if (req.originalUrl.includes('.js') || req.originalUrl.includes('.css')) {
        proxyRes.headers['cache-control'] = 'no-cache, no-store, must-revalidate';
        proxyRes.headers['pragma'] = 'no-cache';
        proxyRes.headers['expires'] = '0';
    }

    console.log(`📡 Proxied: ${req.method} ${req.originalUrl} -> Status: ${proxyRes.statusCode} (${proxyRes.headers['content-type'] || 'unknown'})`);
};

// Proxy configuration for DAT One
const datOneProxy = createProxyMiddleware({
    target: 'https://one.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-one': '', // Remove /dat-one prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT One Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Login
const datLoginProxy = createProxyMiddleware({
    target: 'https://login.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        '^/dat-login': '', // Remove /dat-login prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error('❌ DAT Login Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Main Site
const datMainProxy = createProxyMiddleware({
    target: 'https://www.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-main': '', // Remove /dat-main prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT Main Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Comprehensive proxy for all DAT subdomains
const createDATProxy = (target, pathPrefix) => createProxyMiddleware({
    target: target,
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    timeout: 30000,
    proxyTimeout: 30000,
    pathRewrite: {
        [`^${pathPrefix}`]: '', // Remove prefix
    },
    onProxyRes: removeFrameBlocking,
    onProxyReq: (proxyReq, req, res) => {
        // Add proper headers for the request
        proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        proxyReq.setHeader('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8');
        proxyReq.setHeader('Accept-Language', 'en-US,en;q=0.5');
        proxyReq.setHeader('Accept-Encoding', 'gzip, deflate, br');
        proxyReq.setHeader('Connection', 'keep-alive');
        proxyReq.setHeader('Upgrade-Insecure-Requests', '1');
    },
    onError: (err, req, res) => {
        console.error(`❌ Proxy Error for ${pathPrefix}:`, err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Middleware to handle missing JS/CSS files by redirecting to /dat-one/
app.use((req, res, next) => {
    // Check if this is a JS/CSS file request that should be under /dat-one/
    if ((req.path.endsWith('.js') || req.path.endsWith('.css')) && !req.path.startsWith('/dat-')) {
        console.log(`🔄 Redirecting ${req.path} to /dat-one${req.path}`);
        return res.redirect(`/dat-one${req.path}`);
    }
    next();
});

// Apply comprehensive proxy middleware
app.use('/dat-one', datOneProxy);
app.use('/dat-login', datLoginProxy);
app.use('/dat-main', datMainProxy);

// Additional DAT subdomains
app.use('/shared-content-cargo', createDATProxy('https://shared-content-cargo.dat.com', '/shared-content-cargo'));
app.use('/shared-content', createDATProxy('https://shared-content.prod.dat.com', '/shared-content'));
app.use('/my-shipments', createDATProxy('https://one.prod-my-shipments.prod.dat.com', '/my-shipments'));
app.use('/private-network', createDATProxy('https://one.prod-private-network.prod.dat.com', '/private-network'));
app.use('/freight-search', createDATProxy('https://one.freight-search-prod.prod.dat.com', '/freight-search'));
app.use('/company-search', createDATProxy('https://one.company-search-prod.prod.dat.com', '/company-search'));
app.use('/dashboard-assets', createDATProxy('https://one.dashboard.dat.com', '/dashboard-assets'));
app.use('/freight-api', createDATProxy('https://freight.api.dat.com', '/freight-api'));
app.use('/identity-api', createDATProxy('https://identity.api.dat.com', '/identity-api'));

// External services (optional - may have CORS issues)
app.use('/here-maps', createDATProxy('https://js.api.here.com', '/here-maps'));
app.use('/google-maps', createDATProxy('https://maps.googleapis.com', '/google-maps'));

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        proxies: {
            'dat-one': 'https://one.dat.com',
            'dat-login': 'https://login.dat.com',
            'dat-main': 'https://www.dat.com'
        }
    });
});

// API endpoint to test proxy connectivity
app.get('/api/test-proxy/:target', async (req, res) => {
    const { target } = req.params;
    const targetUrls = {
        'dat-one': 'https://one.dat.com',
        'dat-login': 'https://login.dat.com',
        'dat-main': 'https://www.dat.com'
    };
    
    const targetUrl = targetUrls[target];
    if (!targetUrl) {
        return res.status(400).json({ error: 'Invalid target' });
    }
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(targetUrl, {
            method: 'HEAD',
            timeout: 10000
        });
        
        res.json({
            target: target,
            url: targetUrl,
            status: response.status,
            accessible: response.ok,
            headers: Object.fromEntries(response.headers.entries())
        });
    } catch (error) {
        res.json({
            target: target,
            url: targetUrl,
            accessible: false,
            error: error.message
        });
    }
});

// Default route
app.get('/', (req, res) => {
    res.send(`
        <h1>🚀 DAT Comprehensive iframe Proxy Server</h1>
        <p>Reverse proxy to bypass X-Frame-Options for ALL DAT websites and subdomains</p>
        <h2>Main DAT Proxies:</h2>
        <ul>
            <li><a href="/dat-one/dashboard" target="_blank">DAT One Dashboard</a> → <code>/dat-one/*</code></li>
            <li><a href="/dat-login" target="_blank">DAT Login</a> → <code>/dat-login/*</code></li>
            <li><a href="/dat-main" target="_blank">DAT Main Site</a> → <code>/dat-main/*</code></li>
        </ul>
        <h2>DAT Subdomain Proxies:</h2>
        <ul>
            <li><strong>Assets:</strong> <code>/shared-content-cargo/*</code> → shared-content-cargo.dat.com</li>
            <li><strong>Shared Content:</strong> <code>/shared-content/*</code> → shared-content.prod.dat.com</li>
            <li><strong>My Shipments:</strong> <code>/my-shipments/*</code> → one.prod-my-shipments.prod.dat.com</li>
            <li><strong>Private Network:</strong> <code>/private-network/*</code> → one.prod-private-network.prod.dat.com</li>
            <li><strong>Freight Search:</strong> <code>/freight-search/*</code> → one.freight-search-prod.prod.dat.com</li>
            <li><strong>Company Search:</strong> <code>/company-search/*</code> → one.company-search-prod.prod.dat.com</li>
            <li><strong>Dashboard Assets:</strong> <code>/dashboard-assets/*</code> → one.dashboard.dat.com</li>
        </ul>
        <h2>DAT API Proxies:</h2>
        <ul>
            <li><strong>Freight API:</strong> <code>/freight-api/*</code> → freight.api.dat.com</li>
            <li><strong>Identity API:</strong> <code>/identity-api/*</code> → identity.api.dat.com</li>
        </ul>
        <h2>External Service Proxies:</h2>
        <ul>
            <li><strong>HERE Maps:</strong> <code>/here-maps/*</code> → js.api.here.com</li>
            <li><strong>Google Maps:</strong> <code>/google-maps/*</code> → maps.googleapis.com</li>
        </ul>
        <h2>Test Pages:</h2>
        <ul>
            <li><a href="/iframe-test.html">iframe Embedding Test</a></li>
            <li><a href="/loading-fix.html">Loading Screen Fix Test</a></li>
            <li><a href="/health">Health Check</a></li>
        </ul>
        <p><strong>Usage:</strong> Use these proxy URLs in your iframes to bypass X-Frame-Options restrictions.</p>
        <p><strong>Note:</strong> This comprehensive proxy handles ALL DAT subdomains and micro-frontends!</p>
    `);
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 DAT Comprehensive iframe Proxy Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`📡 Proxying ALL DAT subdomains:`);
    console.log(`   /dat-one/* → https://one.dat.com/*`);
    console.log(`   /dat-login/* → https://login.dat.com/*`);
    console.log(`   /dat-main/* → https://www.dat.com/*`);
    console.log(`   /shared-content-cargo/* → https://shared-content-cargo.dat.com/*`);
    console.log(`   /shared-content/* → https://shared-content.prod.dat.com/*`);
    console.log(`   /my-shipments/* → https://one.prod-my-shipments.prod.dat.com/*`);
    console.log(`   /private-network/* → https://one.prod-private-network.prod.dat.com/*`);
    console.log(`   /freight-search/* → https://one.freight-search-prod.prod.dat.com/*`);
    console.log(`   /company-search/* → https://one.company-search-prod.prod.dat.com/*`);
    console.log(`   /dashboard-assets/* → https://one.dashboard.dat.com/*`);
    console.log(`   /freight-api/* → https://freight.api.dat.com/*`);
    console.log(`   /identity-api/* → https://identity.api.dat.com/*`);
    console.log(`   /here-maps/* → https://js.api.here.com/*`);
    console.log(`   /google-maps/* → https://maps.googleapis.com/*`);
    console.log(`🎯 Ready to handle ALL DAT micro-frontends and fix loading screen!`);
});
