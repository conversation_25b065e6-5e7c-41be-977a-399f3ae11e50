const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3004;

// Enable CORS for all routes
app.use(cors());

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));

// Custom middleware to remove X-Frame-Options and CSP headers
const removeFrameBlocking = (proxyRes, req, res) => {
    // Remove headers that block iframe embedding
    delete proxyRes.headers['x-frame-options'];
    delete proxyRes.headers['content-security-policy'];
    delete proxyRes.headers['content-security-policy-report-only'];
    
    // Add headers to allow iframe embedding
    proxyRes.headers['x-frame-options'] = 'ALLOWALL';
    
    console.log(`📡 Proxied: ${req.method} ${req.originalUrl} -> Status: ${proxyRes.statusCode}`);
};

// Proxy configuration for DAT One
const datOneProxy = createProxyMiddleware({
    target: 'https://one.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-one': '', // Remove /dat-one prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT One Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Login
const datLoginProxy = createProxyMiddleware({
    target: 'https://login.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-login': '', // Remove /dat-login prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT Login Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Proxy configuration for DAT Main Site
const datMainProxy = createProxyMiddleware({
    target: 'https://www.dat.com',
    changeOrigin: true,
    secure: true,
    followRedirects: true,
    pathRewrite: {
        '^/dat-main': '', // Remove /dat-main prefix
    },
    onProxyRes: removeFrameBlocking,
    onError: (err, req, res) => {
        console.error('❌ DAT Main Proxy Error:', err.message);
        res.status(500).send('Proxy Error: ' + err.message);
    }
});

// Apply proxy middleware
app.use('/dat-one', datOneProxy);
app.use('/dat-login', datLoginProxy);
app.use('/dat-main', datMainProxy);

// Health check endpoint
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        proxies: {
            'dat-one': 'https://one.dat.com',
            'dat-login': 'https://login.dat.com',
            'dat-main': 'https://www.dat.com'
        }
    });
});

// API endpoint to test proxy connectivity
app.get('/api/test-proxy/:target', async (req, res) => {
    const { target } = req.params;
    const targetUrls = {
        'dat-one': 'https://one.dat.com',
        'dat-login': 'https://login.dat.com',
        'dat-main': 'https://www.dat.com'
    };
    
    const targetUrl = targetUrls[target];
    if (!targetUrl) {
        return res.status(400).json({ error: 'Invalid target' });
    }
    
    try {
        const fetch = (await import('node-fetch')).default;
        const response = await fetch(targetUrl, {
            method: 'HEAD',
            timeout: 10000
        });
        
        res.json({
            target: target,
            url: targetUrl,
            status: response.status,
            accessible: response.ok,
            headers: Object.fromEntries(response.headers.entries())
        });
    } catch (error) {
        res.json({
            target: target,
            url: targetUrl,
            accessible: false,
            error: error.message
        });
    }
});

// Default route
app.get('/', (req, res) => {
    res.send(`
        <h1>🚀 DAT iframe Proxy Server</h1>
        <p>Reverse proxy to bypass X-Frame-Options for DAT websites</p>
        <h2>Available Proxies:</h2>
        <ul>
            <li><a href="/dat-one/dashboard" target="_blank">DAT One Dashboard</a> → <code>/dat-one/*</code></li>
            <li><a href="/dat-login" target="_blank">DAT Login</a> → <code>/dat-login/*</code></li>
            <li><a href="/dat-main" target="_blank">DAT Main Site</a> → <code>/dat-main/*</code></li>
        </ul>
        <h2>Test Pages:</h2>
        <ul>
            <li><a href="/iframe-test.html">iframe Embedding Test</a></li>
            <li><a href="/health">Health Check</a></li>
        </ul>
        <p><strong>Usage:</strong> Use these proxy URLs in your iframes to bypass X-Frame-Options restrictions.</p>
    `);
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 DAT iframe Proxy Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`📡 Proxying:`);
    console.log(`   /dat-one/* → https://one.dat.com/*`);
    console.log(`   /dat-login/* → https://login.dat.com/*`);
    console.log(`   /dat-main/* → https://www.dat.com/*`);
    console.log(`🎯 Ready to bypass X-Frame-Options!`);
});
