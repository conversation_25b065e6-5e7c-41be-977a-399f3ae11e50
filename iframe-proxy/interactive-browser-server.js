const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const puppeteer = require('puppeteer');
const fs = require('fs');

class InteractiveBrowserServer {
    constructor() {
        this.app = express();
        this.server = null;
        this.wss = null;
        this.masterBrowser = null;
        this.masterPage = null;
        this.clients = new Set();
    }

    async connectToMasterSession() {
        console.log('🔌 Connecting to existing master session...');
        
        // Load master session data
        if (!fs.existsSync('master-session-data.json')) {
            throw new Error('Master session data not found');
        }
        
        const sessionData = JSON.parse(fs.readFileSync('master-session-data.json', 'utf8'));
        console.log('✅ Master session data loaded');
        
        // Launch browser with same configuration
        this.masterBrowser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-web-security'
            ]
        });
        
        this.masterPage = await this.masterBrowser.newPage();
        await this.masterPage.setViewport({ width: 1280, height: 720 });
        await this.masterPage.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
        
        // Restore session state
        if (sessionData.cookies && Array.isArray(sessionData.cookies)) {
            await this.masterPage.setCookie(...sessionData.cookies);
        }
        
        await this.masterPage.evaluateOnNewDocument((localStorageData, sessionStorageData) => {
            if (localStorageData) {
                const localStorage = JSON.parse(localStorageData);
                for (const [key, value] of Object.entries(localStorage)) {
                    window.localStorage.setItem(key, value);
                }
            }
            
            if (sessionStorageData) {
                const sessionStorage = JSON.parse(sessionStorageData);
                for (const [key, value] of Object.entries(sessionStorage)) {
                    window.sessionStorage.setItem(key, value);
                }
            }
        }, sessionData.localStorage, sessionData.sessionStorage);
        
        // Navigate to current URL
        await this.masterPage.goto(sessionData.currentUrl || 'https://one.dat.com/dashboard', {
            waitUntil: 'networkidle2',
            timeout: 30000
        });
        
        console.log(`✅ Connected to master session at: ${this.masterPage.url()}`);
    }

    setupRoutes() {
        // Serve interactive browser interface
        this.app.get('/interactive', (req, res) => {
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Interactive Master Browser</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: Arial, sans-serif; background: #f5f5f5; overflow: hidden; }
        .header {
            background: #1a365d; color: white; padding: 10px 20px;
            display: flex; justify-content: space-between; align-items: center;
            position: fixed; top: 0; left: 0; right: 0; z-index: 1000; height: 50px;
        }
        .header h1 { font-size: 16px; margin: 0; }
        .status { font-size: 12px; padding: 4px 8px; border-radius: 4px; }
        .status.connected { background: #10b981; }
        .status.disconnected { background: #ef4444; }
        .browser-container { 
            position: fixed; top: 50px; left: 0; right: 0; bottom: 0;
            background: white; overflow: hidden;
        }
        .browser-content {
            width: 100%; height: 100%; overflow: auto;
            transform-origin: top left;
        }
        .loading { 
            display: flex; justify-content: center; align-items: center; 
            height: 100%; font-size: 18px; color: #666; 
        }
        .controls {
            display: flex; gap: 5px;
        }
        .btn {
            background: #4f46e5; color: white; border: none; 
            padding: 6px 12px; border-radius: 4px; cursor: pointer; 
            font-size: 12px;
        }
        .btn:hover { background: #4338ca; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚛 DAT Load Board - Interactive Master Browser</h1>
        <div class="controls">
            <button class="btn" onclick="refreshBrowser()">🔄 Refresh</button>
            <button class="btn" onclick="goBack()">⬅️ Back</button>
            <button class="btn" onclick="goForward()">➡️ Forward</button>
        </div>
        <div class="status disconnected" id="status">🔴 Connecting...</div>
    </div>
    
    <div class="browser-container">
        <div class="loading" id="loading">Connecting to master browser...</div>
        <div id="browser-content" class="browser-content" style="display: none;"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        function connect() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = \`\${protocol}//\${window.location.host}/ws\`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = () => {
                console.log('🔌 Connected to master browser');
                isConnected = true;
                updateStatus('connected', '🟢 LIVE');
                document.getElementById('loading').style.display = 'none';
                document.getElementById('browser-content').style.display = 'block';
                
                // Request initial page content
                sendCommand('getContent');
            };
            
            ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                
                if (data.type === 'content') {
                    updateBrowserContent(data.html);
                } else if (data.type === 'error') {
                    console.error('Browser control error:', data.message);
                }
            };
            
            ws.onclose = () => {
                console.log('🔌 Disconnected from master browser');
                isConnected = false;
                updateStatus('disconnected', '🔴 Disconnected');
                
                // Try to reconnect after 3 seconds
                setTimeout(connect, 3000);
            };
            
            ws.onerror = (error) => {
                console.error('WebSocket error:', error);
            };
        }
        
        function updateStatus(status, text) {
            const statusEl = document.getElementById('status');
            statusEl.className = \`status \${status}\`;
            statusEl.textContent = text;
        }
        
        function updateBrowserContent(html) {
            const contentEl = document.getElementById('browser-content');
            
            // Create iframe to safely display content
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';
            
            contentEl.innerHTML = '';
            contentEl.appendChild(iframe);
            
            // Write content to iframe
            iframe.contentDocument.open();
            iframe.contentDocument.write(html);
            iframe.contentDocument.close();
            
            // Add click event listeners to iframe content
            addInteractionListeners(iframe);
        }
        
        function addInteractionListeners(iframe) {
            iframe.onload = () => {
                const iframeDoc = iframe.contentDocument;
                
                // Capture all clicks
                iframeDoc.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    if (isConnected) {
                        const rect = iframe.getBoundingClientRect();
                        const x = e.clientX;
                        const y = e.clientY;
                        
                        sendCommand('click', { x, y });
                    }
                });
                
                // Capture scroll events
                iframeDoc.addEventListener('wheel', (e) => {
                    e.preventDefault();
                    
                    if (isConnected) {
                        sendCommand('scroll', { deltaY: e.deltaY });
                    }
                });
                
                // Capture form inputs
                const inputs = iframeDoc.querySelectorAll('input, textarea');
                inputs.forEach(input => {
                    input.addEventListener('input', (e) => {
                        if (isConnected) {
                            // Clear field first, then type new value
                            sendCommand('clearAndType', { 
                                selector: getElementSelector(e.target),
                                text: e.target.value 
                            });
                        }
                    });
                });
            };
        }
        
        function getElementSelector(element) {
            if (element.id) return '#' + element.id;
            if (element.name) return '[name="' + element.name + '"]';
            if (element.className) return '.' + element.className.split(' ')[0];
            return element.tagName.toLowerCase();
        }
        
        function sendCommand(type, data = {}) {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type, ...data }));
            }
        }
        
        function refreshBrowser() {
            if (isConnected) {
                sendCommand('refresh');
            }
        }
        
        function goBack() {
            if (isConnected) {
                sendCommand('goBack');
            }
        }
        
        function goForward() {
            if (isConnected) {
                sendCommand('goForward');
            }
        }
        
        // Start connection
        connect();
        
        // Auto-refresh content every 10 seconds
        setInterval(() => {
            if (isConnected) {
                sendCommand('getContent');
            }
        }, 10000);
    </script>
</body>
</html>`;
            res.send(html);
        });

        // Health check endpoint
        this.app.get('/status', async (req, res) => {
            try {
                const url = this.masterPage ? this.masterPage.url() : 'Not connected';
                const title = this.masterPage ? await this.masterPage.title() : 'Not connected';
                
                res.json({
                    connected: !!this.masterPage,
                    url: url,
                    title: title,
                    clients: this.clients.size,
                    timestamp: new Date().toISOString()
                });
            } catch (error) {
                res.status(500).json({ error: error.message });
            }
        });
    }

    setupWebSocket() {
        this.wss = new WebSocket.Server({ 
            server: this.server,
            path: '/ws'
        });
        
        this.wss.on('connection', (ws) => {
            console.log('🔌 Client connected to interactive browser');
            this.clients.add(ws);
            
            ws.on('message', async (message) => {
                try {
                    const data = JSON.parse(message);
                    console.log('📨 Received command:', data.type);
                    
                    switch (data.type) {
                        case 'getContent':
                            const html = await this.masterPage.content();
                            ws.send(JSON.stringify({
                                type: 'content',
                                html: html,
                                url: this.masterPage.url()
                            }));
                            break;
                            
                        case 'click':
                            await this.masterPage.mouse.click(data.x, data.y);
                            console.log(`🖱️ Clicked at (${data.x}, ${data.y})`);
                            // Send updated content
                            setTimeout(async () => {
                                const html = await this.masterPage.content();
                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: this.masterPage.url()
                                }));
                            }, 1000);
                            break;
                            
                        case 'scroll':
                            await this.masterPage.evaluate((deltaY) => {
                                window.scrollBy(0, deltaY);
                            }, data.deltaY);
                            break;
                            
                        case 'clearAndType':
                            if (data.selector && data.text) {
                                await this.masterPage.click(data.selector);
                                await this.masterPage.keyboard.down('Control');
                                await this.masterPage.keyboard.press('a');
                                await this.masterPage.keyboard.up('Control');
                                await this.masterPage.keyboard.type(data.text);
                            }
                            break;
                            
                        case 'refresh':
                            await this.masterPage.reload();
                            setTimeout(async () => {
                                const html = await this.masterPage.content();
                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: this.masterPage.url()
                                }));
                            }, 2000);
                            break;
                            
                        case 'goBack':
                            await this.masterPage.goBack();
                            setTimeout(async () => {
                                const html = await this.masterPage.content();
                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: this.masterPage.url()
                                }));
                            }, 1000);
                            break;
                            
                        case 'goForward':
                            await this.masterPage.goForward();
                            setTimeout(async () => {
                                const html = await this.masterPage.content();
                                ws.send(JSON.stringify({
                                    type: 'content',
                                    html: html,
                                    url: this.masterPage.url()
                                }));
                            }, 1000);
                            break;
                    }
                    
                } catch (error) {
                    console.error('❌ Command error:', error.message);
                    ws.send(JSON.stringify({
                        type: 'error',
                        message: error.message
                    }));
                }
            });
            
            ws.on('close', () => {
                console.log('🔌 Client disconnected');
                this.clients.delete(ws);
            });
        });
    }

    async start(port = 3006) {
        try {
            await this.connectToMasterSession();
            
            this.setupRoutes();
            
            this.server = http.createServer(this.app);
            this.setupWebSocket();
            
            this.server.listen(port, '0.0.0.0', () => {
                console.log(`🌐 Interactive Browser Server running on port ${port}`);
                console.log(`🎮 Interactive DAT Browser: http://147.93.146.10:${port}/interactive`);
                console.log(`📊 Status API: http://147.93.146.10:${port}/status`);
            });
            
        } catch (error) {
            console.error('❌ Failed to start interactive browser server:', error.message);
            process.exit(1);
        }
    }

    async stop() {
        if (this.masterBrowser) {
            await this.masterBrowser.close();
        }
        if (this.server) {
            this.server.close();
        }
    }
}

// Start the server if run directly
if (require.main === module) {
    const server = new InteractiveBrowserServer();
    server.start(3006);
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('\n🛑 Shutting down interactive browser server...');
        await server.stop();
        process.exit(0);
    });
}

module.exports = InteractiveBrowserServer;
