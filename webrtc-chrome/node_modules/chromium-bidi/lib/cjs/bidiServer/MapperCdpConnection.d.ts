/**
 * Copyright 2021 Google LLC.
 * Copyright (c) Microsoft Corporation.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import type { MapperOptions } from '../bidiMapper/BidiServer.js';
import type { MapperCdpConnection } from '../cdp/CdpConnection.js';
import { SimpleTransport } from './SimpleTransport.js';
export declare class MapperServerCdpConnection {
    #private;
    static create(cdpConnection: MapperCdpConnection, mapperTabSource: string, verbose: boolean, mapperOptions: MapperOptions): Promise<MapperServerCdpConnection>;
    private constructor();
    close(): void;
    bidiSession(): SimpleTransport;
}
