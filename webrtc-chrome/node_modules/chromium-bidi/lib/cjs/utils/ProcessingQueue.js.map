{"version": 3, "file": "ProcessingQueue.js", "sourceRoot": "", "sources": ["../../../src/utils/ProcessingQueue.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,qCAAgD;AAGhD,MAAa,eAAe;IAC1B,MAAM,CAAU,aAAa,GAAG,GAAG,gBAAO,CAAC,KAAK,QAAiB,CAAC;IAEzD,OAAO,CAAY;IACnB,UAAU,CAA4B;IACtC,MAAM,GAAmC,EAAE,CAAC;IAErD,wCAAwC;IACxC,aAAa,GAAG,KAAK,CAAC;IAEtB,YAAY,SAAoC,EAAE,MAAiB;QACjE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;IACxB,CAAC;IAED,GAAG,CAAC,KAAyB,EAAE,IAAY;QACzC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;QAChC,2DAA2D;QAC3D,KAAK,IAAI,CAAC,gBAAgB,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,gBAAgB;QACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC9B,MAAM,UAAU,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACvC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,SAAS;YACX,CAAC;YACD,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC,GAAG,UAAU,CAAC;YACxC,IAAI,CAAC,OAAO,EAAE,CAAC,eAAe,CAAC,aAAa,EAAE,mBAAmB,EAAE,IAAI,CAAC,CAAC;YAEzE,MAAM,YAAY;iBACf,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;gBACd,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;oBAC3B,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,UAAU,EAClB,6BAA6B,EAC7B,KAAK,CAAC,KAAK,CAAC,OAAO,EACnB,KAAK,CAAC,KAAK,CAAC,KAAK,CAClB,CAAC;oBACF,OAAO;gBACT,CAAC;gBACD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACtC,CAAC,CAAC;iBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;gBACf,IAAI,CAAC,OAAO,EAAE,CACZ,gBAAO,CAAC,UAAU,EAClB,0BAA0B,EAC1B,KAAK,EAAE,OAAO,CACf,CAAC;YACJ,CAAC,CAAC,CAAC;QACP,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;IAC7B,CAAC;;AAzDH,0CA0DC"}