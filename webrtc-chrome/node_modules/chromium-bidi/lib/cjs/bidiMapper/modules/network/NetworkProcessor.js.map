{"version": 3, "file": "NetworkProcessor.js", "sourceRoot": "", "sources": ["../../../../../src/bidiMapper/modules/network/NetworkProcessor.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AAEH,+DAKuC;AAMvC,0CAA0C;AAC1C,MAAa,gBAAgB;IAClB,uBAAuB,CAAyB;IAChD,eAAe,CAAiB;IAEzC,YACE,sBAA8C,EAC9C,cAA8B;QAE9B,IAAI,CAAC,uBAAuB,GAAG,sBAAsB,CAAC;QACtD,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,YAAY,CAChB,MAAsC;QAEtC,IAAI,CAAC,uBAAuB,CAAC,0BAA0B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEzE,MAAM,WAAW,GAAyB,MAAM,CAAC,WAAW,IAAI,EAAE,CAAC;QACnE,MAAM,iBAAiB,GACrB,gBAAgB,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC;QAEjD,MAAM,SAAS,GAAsB,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC;YACrE,WAAW,EAAE,iBAAiB;YAC9B,MAAM,EAAE,MAAM,CAAC,MAAM;YACrB,QAAQ,EAAE,MAAM,CAAC,QAAQ;SAC1B,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5D,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;QACjD,CAAC,CAAC,CACH,CAAC;QAEF,OAAO;YACL,SAAS;SACV,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,IAAI,MAAM,CAAC,GAAG,KAAK,SAAS,EAAE,CAAC;YAC7B,gBAAgB,CAAC,cAAc,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;QAED,IAAI,MAAM,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;YAChC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnD,MAAM,IAAI,sCAAwB,CAChC,WAAW,MAAM,CAAC,MAAM,eAAe,CACxC,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,OAAO,EAAE;;SAE7D,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAA0C;QAE1C,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,OAAO,EAAE;;;SAG7D,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QACzC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CACpB,MAA0C;QAE1C,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE;;SAExD,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;QAEvC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAChB,OAAO,EAAE,SAAS,GACY;QAC9B,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAClD,IAAI,OAAO,CAAC,cAAc,6DAAwC,EAAE,CAAC;YACnE,MAAM,IAAI,sCAAwB,CAChC,YAAY,SAAS,4CAA4C,CAClE,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,IAAI,oCAAsB,CAC9B,4CAA4C,SAAS,GAAG,CACzD,CAAC;QACJ,CAAC;QAED,MAAM,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEpC,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,OAAO,EAAE;;;;SAI7D,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,gBAAgB,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACtD,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAyC;QAEzC,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;QAEvD,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,EAAE;YAC5D,OAAO,OAAO,CAAC,SAAS,CAAC,mBAAmB,EAAE,CAAC;QACjD,CAAC,CAAC,CACH,CAAC;QAEF,OAAO,EAAE,CAAC;IACZ,CAAC;IAED,iBAAiB,CAAC,EAAmB;QACnC,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;QACxD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,oCAAsB,CAC9B,4BAA4B,EAAE,iBAAiB,CAChD,CAAC;QACJ,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,wBAAwB,CACtB,EAAmB,EACnB,MAAgC;QAEhC,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,IAAI,oCAAsB,CAC9B,4CAA4C,EAAE,GAAG,CAClD,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;YACvE,MAAM,IAAI,sCAAwB,CAChC,mCAAmC,EAAE,YAAY,OAAO,CAAC,cAAc,SAAS,CACjF,CAAC;QACJ,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,eAAe,CAAC,OAAyB;QAC9C,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,IAAI,WAAmB,CAAC;YACxB,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBACnC,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;YACzC,CAAC;YAED,IACE,WAAW,KAAK,WAAW,CAAC,IAAI,EAAE;gBAClC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC1B,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,EAC1B,CAAC;gBACD,MAAM,IAAI,sCAAwB,CAChC,iBAAiB,WAAW,2BAA2B,CACxD,CAAC;YACJ,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,MAAc;QACjC,wDAAwD;QACxD,OAAO,+BAA+B,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACH,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,IAAI,CAAC;YACH,OAAO,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,sCAAwB,CAAC,gBAAgB,GAAG,MAAM,KAAK,EAAE,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,CACrB,WAAiC;QAEjC,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;YACpC,QAAQ,UAAU,CAAC,IAAI,EAAE,CAAC;gBACxB,KAAK,QAAQ,CAAC,CAAC,CAAC;oBACd,gBAAgB,CAAC,cAAc,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBACpD,OAAO,UAAU,CAAC;gBACpB,CAAC;gBACD,KAAK,SAAS;oBACZ,oCAAoC;oBACpC,IACE,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,IAAI,KAAK,SAAS;wBAC7B,UAAU,CAAC,QAAQ,KAAK,SAAS;wBACjC,UAAU,CAAC,MAAM,KAAK,SAAS,EAC/B,CAAC;wBACD,OAAO,UAAU,CAAC;oBACpB,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;wBACxB,UAAU,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBAC9D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,EAAE,CAAC;4BACjD,MAAM,IAAI,sCAAwB,CAAC,sBAAsB,CAAC,CAAC;wBAC7D,CAAC;oBACH,CAAC;oBACD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;wBACxB,UAAU,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;oBAChE,CAAC;oBACD,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC;wBACpB,UAAU,CAAC,IAAI,GAAG,kBAAkB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACxD,CAAC;oBACD,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;wBACxB,UAAU,CAAC,QAAQ,GAAG,kBAAkB,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;wBAC9D,IAAI,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;4BACnC,UAAU,CAAC,QAAQ,GAAG,IAAI,UAAU,CAAC,QAAQ,EAAE,CAAC;wBAClD,CAAC;wBACD,IACE,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC;4BACjC,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,EACjC,CAAC;4BACD,MAAM,IAAI,sCAAwB,CAAC,sBAAsB,CAAC,CAAC;wBAC7D,CAAC;oBACH,CAAC;yBAAM,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;wBACtC,UAAU,CAAC,QAAQ,GAAG,GAAG,CAAC;oBAC5B,CAAC;oBAED,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;wBACtB,UAAU,CAAC,MAAM,GAAG,kBAAkB,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;wBAC1D,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC;4BACjC,UAAU,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,CAAC;wBAC9C,CAAC;wBACD,IAAI,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACpC,MAAM,IAAI,sCAAwB,CAAC,sBAAsB,CAAC,CAAC;wBAC7D,CAAC;oBACH,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;wBAC/B,MAAM,IAAI,sCAAwB,CAChC,qCAAqC,CACtC,CAAC;oBACJ,CAAC;oBAED,IAAI,UAAU,CAAC,QAAQ,KAAK,EAAE,EAAE,CAAC;wBAC/B,MAAM,IAAI,sCAAwB,CAChC,qCAAqC,CACtC,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;wBAC3C,IAAI,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;4BACzC,MAAM,IAAI,sCAAwB,CAChC,uCAAuC,CACxC,CAAC;wBACJ,CAAC;wBAED,IAAI,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;4BACvC,MAAM,IAAI,sCAAwB,CAChC,+CAA+C,CAChD,CAAC;wBACJ,CAAC;oBACH,CAAC;oBAED,IAAI,UAAU,CAAC,IAAI,KAAK,EAAE,EAAE,CAAC;wBAC3B,MAAM,IAAI,sCAAwB,CAChC,iCAAiC,CAClC,CAAC;oBACJ,CAAC;oBAED,IAAI,CAAC;wBACH,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;oBAC7B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,IAAI,sCAAwB,CAAC,GAAG,KAAK,EAAE,CAAC,CAAC;oBACjD,CAAC;oBAED,OAAO,UAAU,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,KAAU;QACrC,oHAAoH;QACpH,IAAI,KAAK,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC9C,OAAO,IAAI,sCAAwB,CAAC,gBAAgB,CAAC,CAAC;QACxD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CACF;AAlVD,4CAkVC;AAED;;GAEG;AACH,SAAS,kBAAkB,CAAC,OAAe;IACzC,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACrD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,IAAI,SAAS,GAAG,KAAK,CAAC;IACtB,KAAK,MAAM,CAAC,IAAI,OAAO,EAAE,CAAC;QACxB,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,IAAI,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrB,MAAM,IAAI,sCAAwB,CAAC,sBAAsB,CAAC,CAAC;YAC7D,CAAC;YACD,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;gBACf,SAAS,GAAG,IAAI,CAAC;gBACjB,SAAS;YACX,CAAC;QACH,CAAC;QACD,MAAM,IAAI,CAAC,CAAC;QACZ,SAAS,GAAG,KAAK,CAAC;IACpB,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}