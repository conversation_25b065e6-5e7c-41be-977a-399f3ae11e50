/**
 * @license
 * Copyright 2017 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
export * from './index-browser.js';
import { Puppeteer } from './common/Puppeteer.js';
/**
 * @public
 */
declare const puppeteer: Puppeteer;
export declare const 
/**
 * @public
 */
connect: (options: import("./index-browser.js").ConnectOptions) => Promise<import("./index-browser.js").Browser>;
export default puppeteer;
//# sourceMappingURL=puppeteer-core-browser.d.ts.map