{"version": 3, "file": "httpUtil.js", "sourceRoot": "", "sources": ["../../src/httpUtil.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,2BAAqC;AACrC,2CAA6B;AAC7B,6CAA+B;AAC/B,6BAA0C;AAE1C,6CAAuC;AAEvC,SAAgB,eAAe,CAAC,GAAQ;IACtC,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE;QAC3B,MAAM,OAAO,GAAG,WAAW,CACzB,GAAG,EACH,MAAM,EACN,QAAQ,CAAC,EAAE;YACT,0CAA0C;YAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;YAClB,OAAO,CAAC,QAAQ,CAAC,UAAU,KAAK,GAAG,CAAC,CAAC;QACvC,CAAC,EACD,KAAK,CACN,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACvB,OAAO,CAAC,KAAK,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAhBD,0CAgBC;AAED,SAAgB,WAAW,CACzB,GAAQ,EACR,MAAc,EACd,QAA2C,EAC3C,SAAS,GAAG,IAAI;IAEhB,MAAM,OAAO,GAAwB;QACnC,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,QAAQ,EAAE,GAAG,CAAC,QAAQ;QACtB,IAAI,EAAE,GAAG,CAAC,IAAI;QACd,IAAI,EAAE,GAAG,CAAC,QAAQ,GAAG,GAAG,CAAC,MAAM;QAC/B,MAAM;QACN,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC,EAAC,UAAU,EAAE,YAAY,EAAC,CAAC,CAAC,CAAC,SAAS;QAC3D,IAAI,EAAE,IAAA,sBAAgB,EAAC,GAAG,CAAC,CAAC,IAAI;QAChC,KAAK,EAAE,IAAI,wBAAU,EAAE;KACxB,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAQ,EAAE;QAC1D,IACE,GAAG,CAAC,UAAU;YACd,GAAG,CAAC,UAAU,IAAI,GAAG;YACrB,GAAG,CAAC,UAAU,GAAG,GAAG;YACpB,GAAG,CAAC,OAAO,CAAC,QAAQ,EACpB,CAAC;YACD,WAAW,CAAC,IAAI,SAAG,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC7D,0CAA0C;YAC1C,oDAAoD;YACpD,GAAG,CAAC,MAAM,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,QAAQ,CAAC,GAAG,CAAC,CAAC;QAChB,CAAC;IACH,CAAC,CAAC;IACF,MAAM,OAAO,GACX,OAAO,CAAC,QAAQ,KAAK,QAAQ;QAC3B,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC;QACzC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,EAAE,CAAC;IACd,OAAO,OAAO,CAAC;AACjB,CAAC;AAtCD,kCAsCC;AAED;;GAEG;AACH,SAAgB,YAAY,CAC1B,GAAQ,EACR,eAAuB,EACvB,gBAAwE;IAExE,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAC3C,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,UAAU,GAAG,CAAC,CAAC;QAEnB,SAAS,MAAM,CAAC,KAAa;YAC3B,eAAe,IAAI,KAAK,CAAC,MAAM,CAAC;YAChC,gBAAiB,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,EAAE;YACjD,IAAI,QAAQ,CAAC,UAAU,KAAK,GAAG,EAAE,CAAC;gBAChC,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,yCAAyC,QAAQ,CAAC,UAAU,UAAU,GAAG,EAAE,CAC5E,CAAC;gBACF,0CAA0C;gBAC1C,QAAQ,CAAC,MAAM,EAAE,CAAC;gBAClB,MAAM,CAAC,KAAK,CAAC,CAAC;gBACd,OAAO;YACT,CAAC;YACD,MAAM,IAAI,GAAG,IAAA,sBAAiB,EAAC,eAAe,CAAC,CAAC;YAChD,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBACrB,OAAO,OAAO,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;gBACvB,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACpB,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAE,EAAE,EAAE,CAAC,CAAC;YAC/D,IAAI,gBAAgB,EAAE,CAAC;gBACrB,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE;YAC1B,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AAzCD,oCAyCC;AAEM,KAAK,UAAU,OAAO,CAAC,GAAQ;IACpC,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC;IAChC,IAAI,CAAC;QACH,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;IAAC,MAAM,CAAC;QACP,MAAM,IAAI,KAAK,CAAC,4BAA4B,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;IACjE,CAAC;AACH,CAAC;AAPD,0BAOC;AAED,SAAgB,OAAO,CAAC,GAAQ;IAC9B,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,OAAO,GAAG,WAAW,CACzB,GAAG,EACH,KAAK,EACL,QAAQ,CAAC,EAAE;YACT,IAAI,IAAI,GAAG,EAAE,CAAC;YACd,IAAI,QAAQ,CAAC,UAAU,IAAI,QAAQ,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;gBACtD,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,mBAAmB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;YACrE,CAAC;YACD,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE;gBAC1B,IAAI,IAAI,KAAK,CAAC;YAChB,CAAC,CAAC,CAAC;YACH,QAAQ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;gBACtB,IAAI,CAAC;oBACH,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;gBAC/B,CAAC;gBAAC,MAAM,CAAC;oBACP,OAAO,MAAM,CAAC,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC,CAAC;gBACvD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,EACD,KAAK,CACN,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YACxB,MAAM,CAAC,GAAG,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC;AA3BD,0BA2BC"}