{"version": 3, "file": "detectPlatform.js", "sourceRoot": "", "sources": ["../../src/detectPlatform.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,EAAE,MAAM,IAAI,CAAC;AAEpB,OAAO,EAAC,eAAe,EAAC,MAAM,gCAAgC,CAAC;AAE/D;;GAEG;AACH,MAAM,UAAU,qBAAqB;IACnC,MAAM,QAAQ,GAAG,EAAE,CAAC,QAAQ,EAAE,CAAC;IAC/B,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,OAAO;gBAC1B,CAAC,CAAC,eAAe,CAAC,OAAO;gBACzB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC;QAC1B,KAAK,OAAO;YACV,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B,KAAK,OAAO;YACV,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,KAAK;gBACxB,4CAA4C;gBAC5C,CAAC,EAAE,CAAC,IAAI,EAAE,KAAK,OAAO,IAAI,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC;gBACpD,CAAC,CAAC,eAAe,CAAC,KAAK;gBACvB,CAAC,CAAC,eAAe,CAAC,KAAK,CAAC;QAC5B;YACE,OAAO,SAAS,CAAC;IACrB,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,OAAe;IAClC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACjC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAW,EAAE,EAAE,CAAC,CAAC;QAC/C,OAAO,CACL,KAAK,GAAG,EAAE;YACV,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;YAC3B,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,CAChD,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}