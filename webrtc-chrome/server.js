const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const fs = require('fs');

const app = express();
const server = http.createServer(app);
const io = socketIo(server, {
    cors: {
        origin: "*",
        methods: ["GET", "POST"]
    }
});

const PORT = 3002;

// Session management
const sessions = new Map();

class ChromeSession {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.browser = null;
        this.page = null;
        this.isRecording = false;
        this.socket = null;
        this.lastActivity = Date.now();
    }

    async initialize() {
        console.log(`🚀 Initializing Chrome session: ${this.sessionId}`);
        
        try {
            // Launch Chrome with screen recording capabilities
            this.browser = await puppeteer.launch({
                headless: true, // Start with headless for now
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ],
                defaultViewport: { width: 1920, height: 1080 }
            });

            this.page = await this.browser.newPage();
            
            // Set user agent
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
            
            // Navigate to DAT
            await this.page.goto('https://one.dat.com/dashboard', {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });

            console.log(`✅ Chrome session initialized: ${this.sessionId}`);
            return true;

        } catch (error) {
            console.error(`❌ Failed to initialize Chrome session ${this.sessionId}:`, error);
            return false;
        }
    }

    async startScreenCapture() {
        if (!this.page || this.isRecording) return;

        try {
            console.log(`📹 Starting screen capture for session: ${this.sessionId}`);

            // Take periodic screenshots instead of video stream for now
            this.screenshotInterval = setInterval(async () => {
                try {
                    const screenshot = await this.page.screenshot({
                        type: 'jpeg',
                        quality: 80,
                        fullPage: false
                    });

                    // Convert to base64 and emit to client
                    const base64 = screenshot.toString('base64');
                    if (this.socket) {
                        this.socket.emit('screen-update', {
                            image: `data:image/jpeg;base64,${base64}`,
                            timestamp: Date.now()
                        });
                    }
                } catch (error) {
                    console.error(`❌ Screenshot error for ${this.sessionId}:`, error);
                }
            }, 100); // 10 FPS for now

            this.isRecording = true;
            console.log(`✅ Screen capture started for session: ${this.sessionId}`);

        } catch (error) {
            console.error(`❌ Failed to start screen capture for ${this.sessionId}:`, error);
        }
    }

    async handleMouseEvent(event) {
        if (!this.page) return;

        try {
            const { type, x, y, button } = event;
            
            switch (type) {
                case 'click':
                    await this.page.mouse.click(x, y, { button: button || 'left' });
                    break;
                case 'move':
                    await this.page.mouse.move(x, y);
                    break;
                case 'down':
                    await this.page.mouse.down({ button: button || 'left' });
                    break;
                case 'up':
                    await this.page.mouse.up({ button: button || 'left' });
                    break;
            }

            this.lastActivity = Date.now();
            console.log(`🖱️ Mouse ${type} at (${x}, ${y}) for session: ${this.sessionId}`);

        } catch (error) {
            console.error(`❌ Mouse event error for ${this.sessionId}:`, error);
        }
    }

    async handleKeyboardEvent(event) {
        if (!this.page) return;

        try {
            const { type, key, text } = event;

            switch (type) {
                case 'keydown':
                    await this.page.keyboard.down(key);
                    break;
                case 'keyup':
                    await this.page.keyboard.up(key);
                    break;
                case 'type':
                    await this.page.keyboard.type(text);
                    break;
            }

            this.lastActivity = Date.now();
            console.log(`⌨️ Keyboard ${type} (${key || text}) for session: ${this.sessionId}`);

        } catch (error) {
            console.error(`❌ Keyboard event error for ${this.sessionId}:`, error);
        }
    }

    async navigate(url) {
        if (!this.page) return;

        try {
            console.log(`🌐 Navigating session ${this.sessionId} to: ${url}`);
            await this.page.goto(url, {
                waitUntil: 'domcontentloaded',
                timeout: 30000
            });
            this.lastActivity = Date.now();
        } catch (error) {
            console.error(`❌ Navigation error for ${this.sessionId}:`, error);
        }
    }

    async cleanup() {
        console.log(`🧹 Cleaning up session: ${this.sessionId}`);

        try {
            // Clear screenshot interval
            if (this.screenshotInterval) {
                clearInterval(this.screenshotInterval);
            }

            if (this.page) {
                await this.page.close();
            }
            if (this.browser) {
                await this.browser.close();
            }
        } catch (error) {
            console.error(`❌ Cleanup error for ${this.sessionId}:`, error);
        }

        sessions.delete(this.sessionId);
    }
}

// Serve static files
app.use(express.static('public'));
app.use(express.json());

// API Routes
app.post('/api/create-session', async (req, res) => {
    const sessionId = uuidv4();
    const session = new ChromeSession(sessionId);
    
    const initialized = await session.initialize();
    
    if (initialized) {
        sessions.set(sessionId, session);
        res.json({ success: true, sessionId });
        console.log(`✅ Created session: ${sessionId}`);
    } else {
        res.status(500).json({ success: false, error: 'Failed to initialize session' });
    }
});

app.get('/api/sessions', (req, res) => {
    const sessionList = Array.from(sessions.keys()).map(id => ({
        sessionId: id,
        lastActivity: sessions.get(id).lastActivity,
        isRecording: sessions.get(id).isRecording
    }));
    
    res.json({ sessions: sessionList });
});

// Socket.IO connection handling
io.on('connection', (socket) => {
    console.log(`🔌 Client connected: ${socket.id}`);
    
    socket.on('join-session', async (sessionId) => {
        const session = sessions.get(sessionId);
        if (session) {
            session.socket = socket;
            socket.sessionId = sessionId;
            socket.join(sessionId);
            
            // Start screen capture
            await session.startScreenCapture();
            
            socket.emit('session-ready', { sessionId });
            console.log(`👤 Client joined session: ${sessionId}`);
        } else {
            socket.emit('session-error', { error: 'Session not found' });
        }
    });

    socket.on('mouse-event', async (event) => {
        const session = sessions.get(socket.sessionId);
        if (session) {
            await session.handleMouseEvent(event);
        }
    });

    socket.on('keyboard-event', async (event) => {
        const session = sessions.get(socket.sessionId);
        if (session) {
            await session.handleKeyboardEvent(event);
        }
    });

    socket.on('navigate', async (url) => {
        const session = sessions.get(socket.sessionId);
        if (session) {
            await session.navigate(url);
        }
    });

    socket.on('disconnect', () => {
        console.log(`🔌 Client disconnected: ${socket.id}`);
        // Note: We don't cleanup session immediately in case of reconnection
    });
});

// Cleanup inactive sessions
setInterval(() => {
    const now = Date.now();
    const timeout = 30 * 60 * 1000; // 30 minutes

    for (const [sessionId, session] of sessions) {
        if (now - session.lastActivity > timeout) {
            console.log(`🧹 Cleaning up inactive session: ${sessionId}`);
            session.cleanup();
        }
    }
}, 5 * 60 * 1000); // Check every 5 minutes

// Start server
server.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 WebRTC Chrome Streaming Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log('🎯 Ready to stream Chrome sessions!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🧹 Shutting down...');
    
    // Cleanup all sessions
    for (const session of sessions.values()) {
        await session.cleanup();
    }
    
    process.exit(0);
});
