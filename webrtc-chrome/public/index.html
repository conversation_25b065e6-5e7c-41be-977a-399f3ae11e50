<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC Chrome Streaming - DAT Access</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            background: #2d2d2d;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0052a3;
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .btn.success {
            background: #00aa44;
        }

        .btn.success:hover {
            background: #008833;
        }

        .btn.danger {
            background: #cc0000;
        }

        .btn.danger:hover {
            background: #aa0000;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.connecting {
            background: #ff9900;
            color: #000;
        }

        .status.connected {
            background: #00aa44;
            color: #fff;
        }

        .status.error {
            background: #cc0000;
            color: #fff;
        }

        .main-container {
            height: calc(100vh - 60px);
            display: flex;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-right: 1px solid #444;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #00ff88;
        }

        .session-info {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .session-info h4 {
            margin-bottom: 10px;
            color: #ffffff;
        }

        .session-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #ccc;
        }

        .quick-actions {
            margin-top: 20px;
        }

        .quick-actions .btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .stream-container {
            flex: 1;
            background: #000;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .stream-video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: crosshair;
        }

        .loading {
            text-align: center;
            color: #ccc;
        }

        .loading .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #444;
            border-top: 3px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        .performance-stats {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .performance-stats h4 {
            margin-bottom: 10px;
            color: #ffffff;
        }

        .stat {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            color: #00ff88;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🚀 WebRTC Chrome Streaming</div>
        <div class="controls">
            <span id="status" class="status connecting">Connecting...</span>
            <button id="createSession" class="btn">Create Session</button>
            <button id="disconnect" class="btn danger" disabled>Disconnect</button>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <h3>Session Control</h3>
            
            <div class="session-info">
                <h4>Current Session</h4>
                <p><strong>ID:</strong> <span id="sessionId">None</span></p>
                <p><strong>Status:</strong> <span id="sessionStatus">Disconnected</span></p>
                <p><strong>Latency:</strong> <span id="latency">-</span></p>
            </div>

            <div class="quick-actions">
                <h4>Quick Navigation</h4>
                <button class="btn" onclick="navigateTo('https://one.dat.com/dashboard')">📊 Dashboard</button>
                <button class="btn" onclick="navigateTo('https://one.dat.com/search-loads-ow')">🚛 Search Loads</button>
                <button class="btn" onclick="navigateTo('https://one.dat.com/my-shipments')">📦 My Shipments</button>
                <button class="btn" onclick="navigateTo('https://login.dat.com')">🔐 Login</button>
            </div>

            <div class="performance-stats">
                <h4>Performance</h4>
                <div class="stat">
                    <span class="stat-label">FPS:</span>
                    <span class="stat-value" id="fps">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Bitrate:</span>
                    <span class="stat-value" id="bitrate">0 kbps</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Resolution:</span>
                    <span class="stat-value" id="resolution">-</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Packets Lost:</span>
                    <span class="stat-value" id="packetsLost">0</span>
                </div>
            </div>
        </div>

        <div class="stream-container">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <h3>Initializing Chrome Stream...</h3>
                <p>Setting up WebRTC connection and Chrome instance</p>
            </div>
            
            <img id="streamVideo" class="stream-video" style="display: none;" />
            
            <div class="overlay" id="overlay" style="display: none;">
                <div>🔴 LIVE</div>
                <div>Real-time Chrome streaming</div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="client.js"></script>
</body>
</html>
