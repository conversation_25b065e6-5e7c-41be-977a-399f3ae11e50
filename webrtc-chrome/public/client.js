class WebRTCChromeClient {
    constructor() {
        this.socket = null;
        this.sessionId = null;
        this.isConnected = false;
        this.peerConnection = null;
        this.dataChannel = null;
        this.stats = {
            fps: 0,
            bitrate: 0,
            packetsLost: 0,
            latency: 0
        };
        
        this.initializeUI();
        this.connect();
    }

    initializeUI() {
        // Get UI elements
        this.statusEl = document.getElementById('status');
        this.createSessionBtn = document.getElementById('createSession');
        this.disconnectBtn = document.getElementById('disconnect');
        this.sessionIdEl = document.getElementById('sessionId');
        this.sessionStatusEl = document.getElementById('sessionStatus');
        this.latencyEl = document.getElementById('latency');
        this.loadingEl = document.getElementById('loading');
        this.streamVideo = document.getElementById('streamVideo');
        this.overlay = document.getElementById('overlay');
        
        // Performance stats elements
        this.fpsEl = document.getElementById('fps');
        this.bitrateEl = document.getElementById('bitrate');
        this.resolutionEl = document.getElementById('resolution');
        this.packetsLostEl = document.getElementById('packetsLost');

        // Event listeners
        this.createSessionBtn.addEventListener('click', () => this.createSession());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        
        // Stream video event listeners
        this.streamVideo.addEventListener('click', (e) => this.handleMouseClick(e));
        this.streamVideo.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        
        // Keyboard event listeners
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Prevent context menu on video
        this.streamVideo.addEventListener('contextmenu', (e) => e.preventDefault());
    }

    connect() {
        console.log('🔌 Connecting to WebRTC server...');
        this.updateStatus('connecting', 'Connecting...');
        
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('✅ Connected to server');
            this.isConnected = true;
            this.updateStatus('connected', 'Connected');
            this.createSessionBtn.disabled = false;
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Disconnected from server');
            this.isConnected = false;
            this.updateStatus('error', 'Disconnected');
            this.resetSession();
        });

        this.socket.on('session-ready', (data) => {
            console.log('✅ Session ready:', data.sessionId);
            this.sessionId = data.sessionId;
            this.updateSessionInfo();
            this.initializeWebRTC();
        });

        this.socket.on('session-error', (data) => {
            console.error('❌ Session error:', data.error);
            this.updateStatus('error', `Session Error: ${data.error}`);
        });

        this.socket.on('screen-update', (data) => {
            // Update the video element with the new screenshot
            if (this.streamVideo && data.image) {
                this.streamVideo.src = data.image;
                this.streamVideo.style.display = 'block';
                this.loadingEl.style.display = 'none';
                this.overlay.style.display = 'block';
            }
        });
    }

    async createSession() {
        if (!this.isConnected) return;
        
        console.log('🚀 Creating new Chrome session...');
        this.updateStatus('connecting', 'Creating session...');
        this.createSessionBtn.disabled = true;
        
        try {
            const response = await fetch('/api/create-session', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' }
            });
            
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Session created:', result.sessionId);
                this.socket.emit('join-session', result.sessionId);
                this.disconnectBtn.disabled = false;
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('❌ Failed to create session:', error);
            this.updateStatus('error', `Failed: ${error.message}`);
            this.createSessionBtn.disabled = false;
        }
    }

    async initializeWebRTC() {
        console.log('🎥 Initializing WebRTC connection...');
        
        try {
            // Create peer connection
            this.peerConnection = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });

            // Handle incoming stream
            this.peerConnection.ontrack = (event) => {
                console.log('📺 Received video stream');
                this.streamVideo.srcObject = event.streams[0];
                this.showStream();
            };

            // Create data channel for input events
            this.dataChannel = this.peerConnection.createDataChannel('input', {
                ordered: true
            });

            this.dataChannel.onopen = () => {
                console.log('📡 Data channel opened');
            };

            // For now, we'll simulate the stream since we need more complex WebRTC setup
            this.simulateStream();
            
        } catch (error) {
            console.error('❌ WebRTC initialization failed:', error);
            this.updateStatus('error', 'WebRTC failed');
        }
    }

    simulateStream() {
        // For demonstration, we'll show a placeholder
        // In production, this would be replaced with actual WebRTC stream
        console.log('🎭 Simulating Chrome stream (placeholder)');
        
        this.loadingEl.style.display = 'none';
        this.streamVideo.style.display = 'block';
        this.overlay.style.display = 'block';
        
        // Create a canvas to simulate Chrome content
        const canvas = document.createElement('canvas');
        canvas.width = 1920;
        canvas.height = 1080;
        const ctx = canvas.getContext('2d');
        
        // Draw placeholder content
        ctx.fillStyle = '#1a1a1a';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        
        ctx.fillStyle = '#00ff88';
        ctx.font = '48px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('WebRTC Chrome Stream', canvas.width / 2, canvas.height / 2 - 100);
        
        ctx.fillStyle = '#ffffff';
        ctx.font = '24px Arial';
        ctx.fillText('DAT One Dashboard would appear here', canvas.width / 2, canvas.height / 2 - 50);
        ctx.fillText('Real-time interaction enabled', canvas.width / 2, canvas.height / 2);
        ctx.fillText('Click and type to interact with Chrome', canvas.width / 2, canvas.height / 2 + 50);
        
        ctx.fillStyle = '#0066cc';
        ctx.font = '18px Arial';
        ctx.fillText(`Session ID: ${this.sessionId}`, canvas.width / 2, canvas.height / 2 + 100);
        
        // Convert canvas to video stream
        const stream = canvas.captureStream(30);
        this.streamVideo.srcObject = stream;
        
        this.updateStatus('connected', 'Streaming');
        this.updateSessionInfo();
        this.startPerformanceMonitoring();
    }

    showStream() {
        this.loadingEl.style.display = 'none';
        this.streamVideo.style.display = 'block';
        this.overlay.style.display = 'block';
        this.updateStatus('connected', 'Streaming');
        this.startPerformanceMonitoring();
    }

    handleMouseClick(event) {
        if (!this.sessionId) return;
        
        const rect = this.streamVideo.getBoundingClientRect();
        const x = Math.round((event.clientX - rect.left) * (1920 / rect.width));
        const y = Math.round((event.clientY - rect.top) * (1080 / rect.height));
        
        console.log(`🖱️ Mouse click at (${x}, ${y})`);
        
        this.socket.emit('mouse-event', {
            type: 'click',
            x: x,
            y: y,
            button: event.button === 0 ? 'left' : event.button === 2 ? 'right' : 'middle'
        });
        
        // Visual feedback
        this.showClickFeedback(event.clientX, event.clientY);
    }

    handleMouseMove(event) {
        if (!this.sessionId) return;
        
        const rect = this.streamVideo.getBoundingClientRect();
        const x = Math.round((event.clientX - rect.left) * (1920 / rect.width));
        const y = Math.round((event.clientY - rect.top) * (1080 / rect.height));
        
        // Throttle mouse move events
        if (!this.lastMouseMove || Date.now() - this.lastMouseMove > 50) {
            this.socket.emit('mouse-event', {
                type: 'move',
                x: x,
                y: y
            });
            this.lastMouseMove = Date.now();
        }
    }

    handleKeyDown(event) {
        if (!this.sessionId) return;
        
        // Prevent default browser shortcuts
        if (event.ctrlKey || event.altKey || event.metaKey) {
            event.preventDefault();
        }
        
        console.log(`⌨️ Key down: ${event.key}`);
        
        this.socket.emit('keyboard-event', {
            type: 'keydown',
            key: event.key,
            code: event.code,
            ctrlKey: event.ctrlKey,
            altKey: event.altKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    }

    handleKeyUp(event) {
        if (!this.sessionId) return;
        
        console.log(`⌨️ Key up: ${event.key}`);
        
        this.socket.emit('keyboard-event', {
            type: 'keyup',
            key: event.key,
            code: event.code
        });
    }

    showClickFeedback(x, y) {
        const feedback = document.createElement('div');
        feedback.style.position = 'absolute';
        feedback.style.left = x + 'px';
        feedback.style.top = y + 'px';
        feedback.style.width = '20px';
        feedback.style.height = '20px';
        feedback.style.borderRadius = '50%';
        feedback.style.background = 'rgba(0, 255, 136, 0.8)';
        feedback.style.pointerEvents = 'none';
        feedback.style.transform = 'translate(-50%, -50%)';
        feedback.style.animation = 'clickFeedback 0.3s ease-out';
        feedback.style.zIndex = '1000';
        
        document.body.appendChild(feedback);
        
        setTimeout(() => {
            document.body.removeChild(feedback);
        }, 300);
    }

    startPerformanceMonitoring() {
        setInterval(() => {
            this.updatePerformanceStats();
        }, 1000);
    }

    updatePerformanceStats() {
        // Simulate performance stats
        this.stats.fps = 30 + Math.floor(Math.random() * 10);
        this.stats.bitrate = 2000 + Math.floor(Math.random() * 1000);
        this.stats.latency = 20 + Math.floor(Math.random() * 30);
        this.stats.packetsLost = Math.floor(Math.random() * 5);
        
        this.fpsEl.textContent = this.stats.fps;
        this.bitrateEl.textContent = this.stats.bitrate + ' kbps';
        this.latencyEl.textContent = this.stats.latency + 'ms';
        this.packetsLostEl.textContent = this.stats.packetsLost;
        this.resolutionEl.textContent = '1920x1080';
    }

    updateStatus(type, message) {
        this.statusEl.className = `status ${type}`;
        this.statusEl.textContent = message;
    }

    updateSessionInfo() {
        this.sessionIdEl.textContent = this.sessionId || 'None';
        this.sessionStatusEl.textContent = this.sessionId ? 'Active' : 'Disconnected';
    }

    resetSession() {
        this.sessionId = null;
        this.loadingEl.style.display = 'block';
        this.streamVideo.style.display = 'none';
        this.overlay.style.display = 'none';
        this.createSessionBtn.disabled = false;
        this.disconnectBtn.disabled = true;
        this.updateSessionInfo();
    }

    disconnect() {
        if (this.sessionId) {
            console.log('🔌 Disconnecting session...');
            this.socket.disconnect();
            this.resetSession();
        }
    }
}

// Navigation helper function
function navigateTo(url) {
    if (window.chromeClient && window.chromeClient.sessionId) {
        console.log(`🌐 Navigating to: ${url}`);
        window.chromeClient.socket.emit('navigate', url);
    } else {
        alert('Please create a session first');
    }
}

// Add click feedback animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes clickFeedback {
        0% {
            transform: translate(-50%, -50%) scale(0);
            opacity: 1;
        }
        100% {
            transform: translate(-50%, -50%) scale(2);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize client when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chromeClient = new WebRTCChromeClient();
});
