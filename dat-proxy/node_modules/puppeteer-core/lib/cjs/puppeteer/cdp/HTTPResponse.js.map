{"version": 3, "file": "HTTPResponse.js", "sourceRoot": "", "sources": ["../../../../src/cdp/HTTPResponse.ts"], "names": [], "mappings": ";;;AAQA,4DAAwE;AACxE,mDAAkD;AAClD,qEAA6D;AAC7D,qDAA6C;AAC7C,qDAAuD;AAIvD;;GAEG;AACH,MAAa,eAAgB,SAAQ,8BAAY;IAC/C,QAAQ,CAAiB;IACzB,eAAe,GAA+B,IAAI,CAAC;IACnD,mBAAmB,GAAG,sBAAQ,CAAC,MAAM,EAAe,CAAC;IACrD,cAAc,CAAgB;IAC9B,OAAO,CAAS;IAChB,WAAW,CAAS;IACpB,cAAc,CAAU;IACxB,kBAAkB,CAAU;IAC5B,QAAQ,GAA2B,EAAE,CAAC;IACtC,gBAAgB,CAAyB;IACzC,OAAO,CAAyC;IAEhD,YACE,OAAuB,EACvB,eAA0C,EAC1C,SAAiE;QAEjE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC;QAExB,IAAI,CAAC,cAAc,GAAG;YACpB,EAAE,EAAE,eAAe,CAAC,eAAe;YACnC,IAAI,EAAE,eAAe,CAAC,UAAU;SACjC,CAAC;QACF,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC;gBAC7C,eAAe,CAAC,UAAU,CAAC;QAC7B,IAAI,CAAC,cAAc,GAAG,CAAC,CAAC,eAAe,CAAC,aAAa,CAAC;QACtD,IAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC,eAAe,CAAC,iBAAiB,CAAC;QAE9D,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC;QACzE,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC,OAAO,CAAC;QACxE,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAG,KAAK,CAAC;QAC3C,CAAC;QAED,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC,eAAe;YACrD,CAAC,CAAC,IAAI,oCAAe,CAAC,eAAe,CAAC,eAAe,CAAC;YACtD,CAAC,CAAC,IAAI,CAAC;QACT,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,MAAM,IAAI,IAAI,CAAC;IAChD,CAAC;IAED,6BAA6B,CAC3B,SAAiE;QAEjE,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QACD,MAAM,SAAS,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,KAAK,EAAE,CAAC;YAC3C,OAAO;QACT,CAAC;QACD,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAClD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,MAAM,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5B,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO;QACT,CAAC;QACD,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,YAAY,CAAC,GAAW;QACtB,IAAI,GAAG,EAAE,CAAC;YACR,OAAO,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,CAAC;IAC5C,CAAC;IAEQ,aAAa;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC7B,CAAC;IAEQ,GAAG;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;IAC7B,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,UAAU;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,eAAe;QACtB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAEQ,MAAM;QACb,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAEQ,OAAO;QACd,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,mBAAmB;iBAC5C,YAAY,EAAE;iBACd,IAAI,CAAC,KAAK,IAAI,EAAE;gBACf,IAAI,CAAC;oBACH,6EAA6E;oBAC7E,uDAAuD;oBACvD,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAC9C,yBAAyB,EACzB;wBACE,SAAS,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE;qBAC5B,CACF,CAAC;oBAEF,OAAO,IAAA,gCAAkB,EAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACnE,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IACE,KAAK,YAAY,yBAAa;wBAC9B,KAAK,CAAC,eAAe;4BACnB,yCAAyC,EAC3C,CAAC;wBACD,MAAM,IAAI,yBAAa,CACrB,gGAAgG,CACjG,CAAC;oBACJ,CAAC;oBAED,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC,CAAC,CAAC;QACP,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEQ,OAAO;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAEQ,SAAS;QAChB,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IAC/D,CAAC;IAEQ,iBAAiB;QACxB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAEQ,KAAK;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;IAC/B,CAAC;CACF;AApJD,0CAoJC"}