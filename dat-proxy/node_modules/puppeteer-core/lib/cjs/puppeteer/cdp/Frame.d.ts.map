{"version": 3, "file": "Frame.d.ts", "sourceRoot": "", "sources": ["../../../../src/cdp/Frame.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAC;AAEhD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,sBAAsB,CAAC;AACrD,OAAO,KAAK,EAAC,aAAa,EAAC,MAAM,yBAAyB,CAAC;AAC3D,OAAO,KAAK,EAAC,cAAc,EAAC,MAAM,iBAAiB,CAAC;AACpD,OAAO,EAAC,KAAK,EAA8B,MAAM,iBAAiB,CAAC;AACnE,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,gBAAgB,CAAC;AAIvD,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAC;AAGpD,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AACjD,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,cAAc,CAAC;AAC1C,OAAO,KAAK,EAAC,gBAAgB,EAAC,MAAM,uBAAuB,CAAC;AAC5D,OAAO,KAAK,EACV,mBAAmB,EAEpB,MAAM,0BAA0B,CAAC;AAClC,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAC;AAEpD,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,oBAAoB,CAAC;AAC3D,OAAO,EAAC,aAAa,EAAC,MAAM,oBAAoB,CAAC;AAEjD,OAAO,EAEL,KAAK,uBAAuB,EAC7B,MAAM,uBAAuB,CAAC;AAC/B,OAAO,KAAK,EAAC,OAAO,EAAC,MAAM,WAAW,CAAC;AAGvC;;GAEG;AACH,qBAAa,QAAS,SAAQ,KAAK;;IAKjC,aAAa,EAAE,YAAY,CAAC;IAC5B,SAAS,SAAM;IACf,gBAAgB,cAAqB;IAE5B,GAAG,EAAE,MAAM,CAAC;IACZ,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,aAAa,EAAE,aAAa,CAAC;IAEtC,MAAM,EAAE,kBAAkB,CAAC;gBAGzB,YAAY,EAAE,YAAY,EAC1B,OAAO,EAAE,MAAM,EACf,aAAa,EAAE,MAAM,GAAG,SAAS,EACjC,MAAM,EAAE,UAAU;IAqDpB;;;;OAIG;IACH,OAAO,IAAI,UAAU;IAIrB;;;OAGG;IACH,QAAQ,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAI1B,YAAY,CAAC,MAAM,EAAE,UAAU,GAAG,IAAI;IAI7B,IAAI,IAAI,OAAO;IAKT,IAAI,CACjB,GAAG,EAAE,MAAM,EACX,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IA4EhB,iBAAiB,CAC9B,OAAO,GAAE,cAAmB,GAC3B,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAoC/B,IAAa,MAAM,IAAI,UAAU,CAEhC;IAEQ,SAAS,IAAI,aAAa;IAI1B,aAAa,IAAI,aAAa;IAKxB,UAAU,CACvB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE;QACP,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KAC5D,GACL,OAAO,CAAC,IAAI,CAAC;IA0BP,GAAG,IAAI,MAAM;IAIb,WAAW,IAAI,QAAQ,GAAG,IAAI;IAI9B,WAAW,IAAI,QAAQ,EAAE;IAS5B,gBAAgB,CAAC,aAAa,EAAE,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC;IAkBhE,yBAAyB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAe1D,4BAA4B,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;IAmBpD,mBAAmB,CAChC,OAAO,GAAE,kBAAuB,GAC/B,OAAO,CAAC,mBAAmB,CAAC;IAM/B,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,IAAI,CAAC,KAAK,GAAG,IAAI;IAKnD,wBAAwB,CAAC,GAAG,EAAE,MAAM,GAAG,IAAI;IAI3C,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI;IAQvD,iBAAiB,IAAI,IAAI;IAKzB,iBAAiB,IAAI,IAAI;IAIzB,IAAa,QAAQ,IAAI,OAAO,CAE/B;IAEQ,CAAC,aAAa,CAAC,IAAI,IAAI;IAShC,cAAc,IAAI,KAAK;IAIR,YAAY,IAAI,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;CAYhF;AAED;;GAEG;AACH,wBAAgB,wBAAwB,CACtC,cAAc,EAAE,MAAM,GACrB,QAAQ,CAAC,IAAI,CAAC,cAAc,CAO9B"}