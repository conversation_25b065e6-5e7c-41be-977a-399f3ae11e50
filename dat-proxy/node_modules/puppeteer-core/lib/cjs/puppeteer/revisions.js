"use strict";
/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.PUPPETEER_REVISIONS = void 0;
/**
 * @internal
 */
exports.PUPPETEER_REVISIONS = Object.freeze({
    chrome: '138.0.7204.49',
    'chrome-headless-shell': '138.0.7204.49',
    firefox: 'stable_140.0.2',
});
//# sourceMappingURL=revisions.js.map