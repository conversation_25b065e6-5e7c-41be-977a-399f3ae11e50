* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.logo h1 {
    font-size: 2.5em;
    font-weight: bold;
    margin-bottom: 5px;
}

.subtitle {
    font-size: 1.1em;
    opacity: 0.9;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

/* Sections */
.section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #1e3c72;
    margin-bottom: 20px;
    font-size: 1.8em;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 60, 114, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

/* User Selection */
.user-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.user-btn {
    padding: 20px;
    font-size: 18px;
    border-radius: 10px;
}

/* Forms */
.form {
    max-width: 600px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #2a5298;
    box-shadow: 0 0 0 3px rgba(42, 82, 152, 0.1);
}

/* Status Messages */
.status {
    margin-top: 15px;
    padding: 12px;
    border-radius: 6px;
    font-weight: 500;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Results Table */
.table-container {
    overflow-x: auto;
    margin-top: 20px;
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.results-table th {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
}

.results-table td {
    padding: 12px;
    border-bottom: 1px solid #e0e0e0;
}

.results-table tr:hover {
    background: #f8f9fa;
}

.results-table tr:last-child td {
    border-bottom: none;
}

.load-count {
    margin-bottom: 15px;
    font-size: 18px;
    font-weight: 500;
    color: #1e3c72;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #2a5298;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: white;
    font-size: 18px;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .logo h1 {
        font-size: 2em;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .user-buttons {
        grid-template-columns: 1fr;
    }
    
    .results-table {
        font-size: 14px;
    }
    
    .results-table th,
    .results-table td {
        padding: 8px 6px;
    }
}

/* 2FA Styles */
.twofa-info {
    background: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.twofa-info p {
    margin: 0;
    color: #1565c0;
}

#phoneVerificationStep,
#emailVerificationStep {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 15px;
}

#emailVerificationStep {
    border-left: 4px solid #28a745;
}

#emailVerificationStep p {
    margin-bottom: 10px;
}

#emailVerificationStep p:first-child {
    color: #28a745;
    font-weight: 500;
}

#verificationCode {
    font-family: 'Courier New', monospace;
    font-size: 18px;
    text-align: center;
    letter-spacing: 2px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.hidden {
    display: none !important;
}
