<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Load Board - Multi-User Access</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <h1>DAT Load Board</h1>
                <span class="subtitle">Multi-User Access Portal</span>
            </div>
            <div class="user-info">
                <span id="userDisplay">Not logged in</span>
                <button id="logoutBtn" class="btn btn-secondary" style="display: none;">Logout</button>
            </div>
        </header>

        <!-- User Selection -->
        <div id="userSelection" class="section">
            <h2>Select Your Session</h2>
            <div class="user-buttons">
                <button class="btn btn-primary user-btn" data-user="user1">User 1</button>
                <button class="btn btn-primary user-btn" data-user="user2">User 2</button>
                <button class="btn btn-primary user-btn" data-user="user3">User 3</button>
                <button class="btn btn-primary user-btn" data-user="user4">User 4</button>
            </div>
        </div>

        <!-- Login Form -->
        <div id="loginSection" class="section" style="display: none;">
            <h2>Login to DAT</h2>
            <form id="loginForm" class="form">
                <div class="form-group">
                    <label for="username">Username/Email:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit" class="btn btn-primary">Login</button>
            </form>
            <div id="loginStatus" class="status"></div>
        </div>

        <!-- Search Interface -->
        <div id="searchSection" class="section" style="display: none;">
            <h2>Search Loads</h2>
            <form id="searchForm" class="form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="origin">Origin:</label>
                        <input type="text" id="origin" name="origin" placeholder="City, State or ZIP">
                    </div>
                    <div class="form-group">
                        <label for="destination">Destination:</label>
                        <input type="text" id="destination" name="destination" placeholder="City, State or ZIP">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="radius">Radius (miles):</label>
                        <select id="radius" name="radius">
                            <option value="25">25 miles</option>
                            <option value="50" selected>50 miles</option>
                            <option value="100">100 miles</option>
                            <option value="200">200 miles</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="equipment">Equipment Type:</label>
                        <select id="equipment" name="equipment">
                            <option value="">Any</option>
                            <option value="van">Van</option>
                            <option value="reefer">Reefer</option>
                            <option value="flatbed">Flatbed</option>
                            <option value="stepdeck">Step Deck</option>
                        </select>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">Search Loads</button>
            </form>
            <div id="searchStatus" class="status"></div>
        </div>

        <!-- Results Section -->
        <div id="resultsSection" class="section" style="display: none;">
            <h2>Load Results</h2>
            <div id="loadCount" class="load-count"></div>
            <div class="table-container">
                <table id="resultsTable" class="results-table">
                    <thead>
                        <tr>
                            <th>Origin</th>
                            <th>Destination</th>
                            <th>Rate</th>
                            <th>Distance</th>
                            <th>Equipment</th>
                            <th>Company</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loadingOverlay" class="loading-overlay" style="display: none;">
            <div class="loading-spinner"></div>
            <div class="loading-text">Processing...</div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
