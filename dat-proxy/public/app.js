class DATProxyApp {
    constructor() {
        this.currentUser = null;
        this.isLoggedIn = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.showUserSelection();
    }

    bindEvents() {
        // User selection
        document.querySelectorAll('.user-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.selectUser(e.target.dataset.user);
            });
        });

        // Login form
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        // 2FA form
        document.getElementById('twoFAForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handle2FASubmission();
        });

        // Switch to email button
        document.getElementById('switchToEmailBtn').addEventListener('click', () => {
            this.switchToEmailVerification();
        });

        // Search form
        document.getElementById('searchForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleSearch();
        });

        // Logout button
        document.getElementById('logoutBtn').addEventListener('click', () => {
            this.logout();
        });
    }

    showUserSelection() {
        this.hideAllSections();
        document.getElementById('userSelection').style.display = 'block';
        document.getElementById('userDisplay').textContent = 'Select a user session';
    }

    showLogin() {
        this.hideAllSections();
        document.getElementById('loginSection').style.display = 'block';
        document.getElementById('userDisplay').textContent = `User: ${this.currentUser}`;
    }

    show2FA(step = 'phone') {
        this.hideAllSections();
        document.getElementById('twoFASection').style.display = 'block';
        document.getElementById('userDisplay').textContent = `User: ${this.currentUser} (2FA Required)`;

        if (step === 'phone') {
            document.getElementById('phoneVerificationStep').style.display = 'block';
            document.getElementById('emailVerificationStep').style.display = 'none';
        } else if (step === 'email') {
            document.getElementById('phoneVerificationStep').style.display = 'none';
            document.getElementById('emailVerificationStep').style.display = 'block';
        }
    }

    showSearch() {
        this.hideAllSections();
        document.getElementById('searchSection').style.display = 'block';
        document.getElementById('resultsSection').style.display = 'block';
        document.getElementById('logoutBtn').style.display = 'inline-block';
        document.getElementById('userDisplay').textContent = `User: ${this.currentUser} (Logged In)`;
    }

    hideAllSections() {
        document.getElementById('userSelection').style.display = 'none';
        document.getElementById('loginSection').style.display = 'none';
        document.getElementById('twoFASection').style.display = 'none';
        document.getElementById('searchSection').style.display = 'none';
        document.getElementById('resultsSection').style.display = 'none';
        document.getElementById('logoutBtn').style.display = 'none';
    }

    showLoading(show = true) {
        document.getElementById('loadingOverlay').style.display = show ? 'flex' : 'none';
    }

    showStatus(elementId, message, type = 'info') {
        const statusEl = document.getElementById(elementId);
        statusEl.textContent = message;
        statusEl.className = `status ${type}`;
        statusEl.style.display = 'block';
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            statusEl.style.display = 'none';
        }, 5000);
    }

    async selectUser(userId) {
        this.currentUser = userId;
        this.showLoading();

        try {
            const response = await fetch(`/api/session/${userId}`);
            const data = await response.json();

            if (data.success) {
                if (data.isLoggedIn) {
                    this.isLoggedIn = true;
                    this.showSearch();
                } else {
                    this.showLogin();
                }
            } else {
                this.showStatus('loginStatus', 'Failed to create session: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Session error:', error);
            this.showStatus('loginStatus', 'Connection error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async handleLogin() {
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        if (!username || !password) {
            this.showStatus('loginStatus', 'Please enter both username and password', 'error');
            return;
        }

        this.showLoading();
        this.showStatus('loginStatus', 'Logging in...', 'info');

        try {
            const response = await fetch(`/api/login/${this.currentUser}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (data.success) {
                this.isLoggedIn = true;
                this.showStatus('loginStatus', 'Login successful!', 'success');
                setTimeout(() => {
                    this.showSearch();
                }, 1500);
            } else if (data.requires2FA) {
                this.showStatus('loginStatus', 'Credentials accepted. 2FA required...', 'info');
                setTimeout(() => {
                    this.show2FA('phone');
                }, 1500);
            } else {
                this.showStatus('loginStatus', 'Login failed: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showStatus('loginStatus', 'Connection error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async switchToEmailVerification() {
        this.showLoading();
        this.showStatus('switchStatus', 'Switching to email verification...', 'info');

        try {
            const response = await fetch(`/api/switch-to-email/${this.currentUser}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();

            if (data.success) {
                this.showStatus('switchStatus', 'Switched to email! Check your email for the code.', 'success');
                setTimeout(() => {
                    this.show2FA('email');
                }, 2000);
            } else {
                this.showStatus('switchStatus', 'Failed to switch: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Switch to email error:', error);
            this.showStatus('switchStatus', 'Connection error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async handle2FASubmission() {
        const code = document.getElementById('verificationCode').value;

        if (!code || code.length !== 6) {
            this.showStatus('twoFAStatus', 'Please enter a valid 6-digit verification code', 'error');
            return;
        }

        this.showLoading();
        this.showStatus('twoFAStatus', 'Verifying code...', 'info');

        try {
            const response = await fetch(`/api/submit-2fa/${this.currentUser}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ code })
            });

            const data = await response.json();

            if (data.success) {
                this.isLoggedIn = true;
                this.showStatus('twoFAStatus', 'Verification successful! Logging you in...', 'success');
                setTimeout(() => {
                    this.showSearch();
                }, 1500);
            } else {
                this.showStatus('twoFAStatus', 'Verification failed: ' + data.message, 'error');
                // Clear the code input for retry
                document.getElementById('verificationCode').value = '';
            }
        } catch (error) {
            console.error('2FA submission error:', error);
            this.showStatus('twoFAStatus', 'Connection error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    async handleSearch() {
        const searchParams = {
            origin: document.getElementById('origin').value,
            destination: document.getElementById('destination').value,
            radius: document.getElementById('radius').value,
            equipment: document.getElementById('equipment').value
        };

        if (!searchParams.origin && !searchParams.destination) {
            this.showStatus('searchStatus', 'Please enter at least origin or destination', 'error');
            return;
        }

        this.showLoading();
        this.showStatus('searchStatus', 'Searching for loads...', 'info');

        try {
            const response = await fetch(`/api/search/${this.currentUser}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(searchParams)
            });

            const data = await response.json();

            if (data.success) {
                this.displayResults(data.loads);
                this.showStatus('searchStatus', `Found ${data.loads.length} loads`, 'success');
            } else {
                this.showStatus('searchStatus', 'Search failed: ' + data.message, 'error');
            }
        } catch (error) {
            console.error('Search error:', error);
            this.showStatus('searchStatus', 'Connection error. Please try again.', 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displayResults(loads) {
        const tbody = document.getElementById('resultsBody');
        const loadCount = document.getElementById('loadCount');
        
        // Clear previous results
        tbody.innerHTML = '';
        
        // Update load count
        loadCount.textContent = `Found ${loads.length} loads`;

        if (loads.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center">No loads found matching your criteria</td></tr>';
            return;
        }

        // Populate table
        loads.forEach((load, index) => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${this.escapeHtml(load.origin)}</td>
                <td>${this.escapeHtml(load.destination)}</td>
                <td>${this.escapeHtml(load.rate)}</td>
                <td>${this.escapeHtml(load.distance)}</td>
                <td>${this.escapeHtml(load.equipment)}</td>
                <td>${this.escapeHtml(load.company)}</td>
                <td>
                    <button class="btn btn-success btn-sm" onclick="app.contactLoad('${load.id}')">
                        Contact
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    contactLoad(loadId) {
        // This would typically open a contact form or initiate contact
        alert(`Contacting about load ID: ${loadId}\n\nThis feature would typically open a contact form or dial the broker directly.`);
    }

    logout() {
        this.currentUser = null;
        this.isLoggedIn = false;

        // Clear forms
        document.getElementById('loginForm').reset();
        document.getElementById('twoFAForm').reset();
        document.getElementById('searchForm').reset();

        // Clear results
        document.getElementById('resultsBody').innerHTML = '';
        document.getElementById('loadCount').textContent = '';

        this.showUserSelection();
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the application
const app = new DATProxyApp();
