const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false // Allow inline scripts for our custom interface
}));

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Store user sessions (lightweight session tracking)
const userSessions = new Map();

// Single master DAT session that all users share
let masterDATSession = null;

class MasterDATSession {
    constructor() {
        this.browser = null;
        this.page = null;
        this.isLoggedIn = false;
        this.loginInProgress = false;
        this.lastActivity = Date.now();
        this.pendingLogin = null; // Store login promise for concurrent requests
    }

    async initialize() {
        console.log('Initializing Master DAT session...');

        this.browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        this.page = await this.browser.newPage();

        // Set user agent to appear as a regular browser
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Set viewport
        await this.page.setViewport({ width: 1920, height: 1080 });

        // Remove automation indicators
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // Remove chrome automation indicators
            if (window.chrome && window.chrome.runtime) {
                delete window.chrome.runtime.onConnect;
                delete window.chrome.runtime.onMessage;
            }

            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        });

        console.log('Master DAT session initialized');
    }

    async login(username, password) {
        try {
            console.log(`Attempting login for user: ${this.userId}`);

            // Navigate to DAT login page with realistic delays
            await this.page.goto('https://one.dat.com/login', {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Add realistic delay to simulate human behavior
            await this.humanDelay(1000, 2000);

            // Wait for login form
            await this.page.waitForSelector('input[type="email"], input[name="username"], input[id="username"]', { timeout: 10000 });

            // Type credentials with human-like typing speed
            await this.humanType('input[type="email"], input[name="username"], input[id="username"]', username);
            await this.humanDelay(500, 1000);
            await this.humanType('input[type="password"], input[name="password"], input[id="password"]', password);

            // Human delay before clicking submit
            await this.humanDelay(800, 1500);

            // Submit form
            await this.page.click('button[type="submit"], input[type="submit"], .login-button');

            // Wait for response - could be 2FA page or dashboard
            await new Promise(resolve => setTimeout(resolve, 3000));

            const currentUrl = this.page.url();
            console.log(`After login attempt, current URL: ${currentUrl}`);

            // Check if we're on 2FA page
            const is2FAPage = await this.page.$('input[type="tel"], input[name="code"], input[id="code"], .verification-code') !== null ||
                             await this.page.content().toLowerCase().includes('verification') ||
                             await this.page.content().toLowerCase().includes('verify') ||
                             await this.page.content().toLowerCase().includes('authenticate');

            if (is2FAPage) {
                console.log(`2FA required for user: ${this.userId}`);
                return {
                    success: false,
                    requires2FA: true,
                    message: '2FA verification required',
                    step: 'phone_verification'
                };
            }

            // Check if login was successful
            if (currentUrl.includes('dashboard') || currentUrl.includes('loadboard') || currentUrl.includes('one.dat.com') && !currentUrl.includes('login')) {
                this.isLoggedIn = true;
                console.log(`Login successful for user: ${this.userId}`);
                return { success: true, message: 'Login successful' };
            } else {
                console.log(`Login failed for user: ${this.userId}`);
                return { success: false, message: 'Invalid credentials or login failed' };
            }

        } catch (error) {
            console.error(`Login error for user ${this.userId}:`, error);
            return { success: false, message: 'Login error: ' + error.message };
        }
    }

    async switchTo2FAEmail() {
        try {
            console.log(`Switching to email 2FA for user: ${this.userId}`);

            // Look for "Try another method" or similar button
            const switchButtons = [
                'button:contains("Try another method")',
                'button:contains("Use email")',
                'a:contains("Try another method")',
                'a:contains("Use email")',
                '.alternative-method',
                '.email-option'
            ];

            let switchButton = null;
            for (const selector of switchButtons) {
                try {
                    switchButton = await this.page.$(selector);
                    if (switchButton) break;
                } catch (e) {
                    // Continue to next selector
                }
            }

            if (switchButton) {
                await this.humanDelay(1000, 2000);
                await switchButton.click();
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Look for email option
                const emailButtons = [
                    'button:contains("Email")',
                    'button:contains("Send email")',
                    '.email-verification',
                    '[data-method="email"]'
                ];

                for (const selector of emailButtons) {
                    try {
                        const emailBtn = await this.page.$(selector);
                        if (emailBtn) {
                            await this.humanDelay(500, 1000);
                            await emailBtn.click();
                            await new Promise(resolve => setTimeout(resolve, 2000));
                            break;
                        }
                    } catch (e) {
                        // Continue
                    }
                }

                return { success: true, message: 'Switched to email verification' };
            } else {
                return { success: false, message: 'Could not find switch to email option' };
            }

        } catch (error) {
            console.error(`Error switching to email 2FA for user ${this.userId}:`, error);
            return { success: false, message: 'Error switching to email: ' + error.message };
        }
    }

    async submit2FACode(code) {
        try {
            console.log(`Submitting 2FA code for user: ${this.userId}`);

            // Find the verification code input
            const codeInputSelectors = [
                'input[type="tel"]',
                'input[name="code"]',
                'input[id="code"]',
                'input[name="verificationCode"]',
                'input[placeholder*="code"]',
                '.verification-code input',
                '.otp-input'
            ];

            let codeInput = null;
            for (const selector of codeInputSelectors) {
                try {
                    codeInput = await this.page.$(selector);
                    if (codeInput) break;
                } catch (e) {
                    // Continue
                }
            }

            if (!codeInput) {
                return { success: false, message: 'Could not find verification code input field' };
            }

            // Clear any existing value and type the code
            await codeInput.click();
            await this.page.keyboard.selectAll();
            await this.humanType(codeInputSelectors[0], code);

            await this.humanDelay(1000, 2000);

            // Find and click submit button
            const submitSelectors = [
                'button[type="submit"]',
                'button:contains("Verify")',
                'button:contains("Submit")',
                'button:contains("Continue")',
                '.verify-button',
                '.submit-code'
            ];

            let submitButton = null;
            for (const selector of submitSelectors) {
                try {
                    submitButton = await this.page.$(selector);
                    if (submitButton) break;
                } catch (e) {
                    // Continue
                }
            }

            if (submitButton) {
                await submitButton.click();

                // Wait for navigation or response
                await new Promise(resolve => setTimeout(resolve, 3000));

                const currentUrl = this.page.url();
                console.log(`After 2FA submission, current URL: ${currentUrl}`);

                // Check if we're now logged in
                if (currentUrl.includes('dashboard') || currentUrl.includes('loadboard') ||
                    (currentUrl.includes('one.dat.com') && !currentUrl.includes('login') && !currentUrl.includes('verify'))) {
                    this.isLoggedIn = true;
                    console.log(`2FA successful, login complete for user: ${this.userId}`);
                    return { success: true, message: 'Login successful' };
                } else {
                    // Check if there's an error message
                    const errorSelectors = [
                        '.error-message',
                        '.alert-danger',
                        '.invalid-code',
                        '[role="alert"]'
                    ];

                    for (const selector of errorSelectors) {
                        try {
                            const errorElement = await this.page.$(selector);
                            if (errorElement) {
                                const errorText = await errorElement.textContent();
                                return { success: false, message: `2FA failed: ${errorText}` };
                            }
                        } catch (e) {
                            // Continue
                        }
                    }

                    return { success: false, message: 'Invalid verification code' };
                }
            } else {
                return { success: false, message: 'Could not find submit button for verification code' };
            }

        } catch (error) {
            console.error(`Error submitting 2FA code for user ${this.userId}:`, error);
            return { success: false, message: 'Error submitting code: ' + error.message };
        }
    }

    // Helper method to simulate human typing
    async humanType(selector, text) {
        const element = await this.page.$(selector);
        if (element) {
            await element.click();
            for (const char of text) {
                await this.page.keyboard.type(char);
                await new Promise(resolve => setTimeout(resolve, Math.random() * 100 + 50)); // 50-150ms between keystrokes
            }
        }
    }

    // Helper method to simulate human delays
    async humanDelay(min, max) {
        const delay = Math.random() * (max - min) + min;
        await new Promise(resolve => setTimeout(resolve, delay));
    }

    async searchLoads(searchParams) {
        try {
            if (!this.isLoggedIn) {
                return { success: false, message: 'Not logged in' };
            }

            console.log(`Searching loads for user: ${this.userId}`, searchParams);
            
            // Navigate to load board if not already there
            if (!this.page.url().includes('loadboard')) {
                await this.page.goto('https://one.dat.com/loadboard', { 
                    waitUntil: 'networkidle2',
                    timeout: 30000 
                });
            }

            // Fill search form (adjust selectors based on actual DAT interface)
            if (searchParams.origin) {
                await this.page.waitForSelector('#origin, .origin-input, input[placeholder*="origin"]', { timeout: 5000 });
                await this.page.click('#origin, .origin-input, input[placeholder*="origin"]');
                await this.page.keyboard.selectAll();
                await this.page.type('#origin, .origin-input, input[placeholder*="origin"]', searchParams.origin);
            }

            if (searchParams.destination) {
                await this.page.waitForSelector('#destination, .destination-input, input[placeholder*="destination"]', { timeout: 5000 });
                await this.page.click('#destination, .destination-input, input[placeholder*="destination"]');
                await this.page.keyboard.selectAll();
                await this.page.type('#destination, .destination-input, input[placeholder*="destination"]', searchParams.destination);
            }

            // Submit search
            await this.page.click('.search-button, button[type="submit"], .btn-search');
            
            // Wait for results
            await this.page.waitForSelector('.load-results, .results-table, .load-list', { timeout: 10000 });
            
            // Extract load data
            const loads = await this.page.evaluate(() => {
                // This will need to be customized based on DAT's actual HTML structure
                const loadElements = document.querySelectorAll('.load-row, .load-item, tr.load');
                const results = [];
                
                loadElements.forEach((element, index) => {
                    if (index < 20) { // Limit to first 20 results
                        const loadData = {
                            id: element.getAttribute('data-id') || index,
                            origin: element.querySelector('.origin, .pickup')?.textContent?.trim() || '',
                            destination: element.querySelector('.destination, .delivery')?.textContent?.trim() || '',
                            rate: element.querySelector('.rate, .price')?.textContent?.trim() || '',
                            distance: element.querySelector('.distance, .miles')?.textContent?.trim() || '',
                            equipment: element.querySelector('.equipment, .trailer')?.textContent?.trim() || '',
                            company: element.querySelector('.company, .broker')?.textContent?.trim() || ''
                        };
                        results.push(loadData);
                    }
                });
                
                return results;
            });

            this.lastActivity = Date.now();
            
            console.log(`Found ${loads.length} loads for user: ${this.userId}`);
            return { success: true, loads: loads };
            
        } catch (error) {
            console.error(`Search error for user ${this.userId}:`, error);
            return { success: false, message: 'Search error: ' + error.message };
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log(`Browser closed for user: ${this.userId}`);
        }
    }

    updateActivity() {
        this.lastActivity = Date.now();
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log('Master DAT session browser closed');
        }
    }
}

// Simple user session tracking (no browser, just session state)
class UserSession {
    constructor(userId) {
        this.userId = userId;
        this.lastActivity = Date.now();
        this.currentSearch = null;
    }

    updateActivity() {
        this.lastActivity = Date.now();
    }
}

// Initialize master session
async function initializeMasterSession() {
    if (!masterDATSession) {
        masterDATSession = new MasterDATSession();
        await masterDATSession.initialize();
    }
    return masterDATSession;
}

// API Routes

// Get or create user session
app.get('/api/session/:userId', async (req, res) => {
    const userId = req.params.userId;

    try {
        // Create user session if it doesn't exist
        if (!userSessions.has(userId)) {
            const session = new UserSession(userId);
            userSessions.set(userId, session);
        }

        const session = userSessions.get(userId);
        session.updateActivity();

        // Initialize master DAT session if needed
        const master = await initializeMasterSession();

        res.json({
            success: true,
            userId: userId,
            isLoggedIn: master.isLoggedIn
        });
    } catch (error) {
        console.error('Session creation error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Login endpoint - handles login on master session
app.post('/api/login/:userId', async (req, res) => {
    const userId = req.params.userId;
    const { username, password } = req.body;

    try {
        // Ensure user session exists
        if (!userSessions.has(userId)) {
            const session = new UserSession(userId);
            userSessions.set(userId, session);
        }

        const userSession = userSessions.get(userId);
        userSession.updateActivity();

        // Get master session and perform login
        const master = await initializeMasterSession();

        // If already logged in, return success
        if (master.isLoggedIn) {
            return res.json({ success: true, message: 'Already logged in' });
        }

        // If login is in progress, wait for it
        if (master.loginInProgress && master.pendingLogin) {
            const result = await master.pendingLogin;
            return res.json(result);
        }

        // Start login process
        master.loginInProgress = true;
        master.pendingLogin = master.login(username, password);

        const result = await master.pendingLogin;

        master.loginInProgress = false;
        master.pendingLogin = null;

        res.json(result);
    } catch (error) {
        console.error('Login error:', error);
        if (masterDATSession) {
            masterDATSession.loginInProgress = false;
            masterDATSession.pendingLogin = null;
        }
        res.status(500).json({ success: false, message: error.message });
    }
});

// Switch to email 2FA endpoint
app.post('/api/switch-to-email/:userId', async (req, res) => {
    const userId = req.params.userId;

    try {
        const session = userSessions.get(userId);
        if (!session) {
            return res.status(404).json({ success: false, message: 'Session not found' });
        }

        const result = await session.switchTo2FAEmail();
        res.json(result);
    } catch (error) {
        console.error('Switch to email error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Submit 2FA code endpoint
app.post('/api/submit-2fa/:userId', async (req, res) => {
    const userId = req.params.userId;
    const { code } = req.body;

    try {
        const session = userSessions.get(userId);
        if (!session) {
            return res.status(404).json({ success: false, message: 'Session not found' });
        }

        const result = await session.submit2FACode(code);
        res.json(result);
    } catch (error) {
        console.error('2FA submission error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Search loads endpoint
app.post('/api/search/:userId', async (req, res) => {
    const userId = req.params.userId;
    const searchParams = req.body;

    try {
        const session = userSessions.get(userId);
        if (!session) {
            return res.status(404).json({ success: false, message: 'Session not found' });
        }

        const result = await session.searchLoads(searchParams);
        res.json(result);
    } catch (error) {
        console.error('Search error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Cleanup inactive sessions every 30 minutes
setInterval(() => {
    const now = Date.now();
    const thirtyMinutes = 30 * 60 * 1000;
    
    for (const [userId, session] of userSessions.entries()) {
        if (now - session.lastActivity > thirtyMinutes) {
            console.log(`Cleaning up inactive session for user: ${userId}`);
            session.cleanup();
            userSessions.delete(userId);
        }
    }
}, 30 * 60 * 1000);

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`DAT Proxy Server running on port ${PORT}`);
    console.log(`Access the application at: http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    
    // Close all browser sessions
    for (const [userId, session] of userSessions.entries()) {
        await session.cleanup();
    }
    
    process.exit(0);
});
