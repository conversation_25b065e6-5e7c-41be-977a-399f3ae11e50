const express = require('express');
const puppeteer = require('puppeteer');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: false // Allow inline scripts for our custom interface
}));

app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Rate limiting
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Store browser instances for each user session
const userSessions = new Map();

class DATSession {
    constructor(userId) {
        this.userId = userId;
        this.browser = null;
        this.page = null;
        this.isLoggedIn = false;
        this.lastActivity = Date.now();
    }

    async initialize() {
        console.log(`Initializing DAT session for user: ${this.userId}`);
        
        this.browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });

        this.page = await this.browser.newPage();
        
        // Set user agent to appear as a regular browser
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
        
        // Set viewport
        await this.page.setViewport({ width: 1920, height: 1080 });
        
        console.log(`DAT session initialized for user: ${this.userId}`);
    }

    async login(username, password) {
        try {
            console.log(`Attempting login for user: ${this.userId}`);
            
            // Navigate to DAT login page
            await this.page.goto('https://one.dat.com/login', { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });

            // Wait for login form
            await this.page.waitForSelector('input[type="email"], input[name="username"], input[id="username"]', { timeout: 10000 });
            
            // Fill in credentials (you'll need to adjust selectors based on actual DAT login form)
            await this.page.type('input[type="email"], input[name="username"], input[id="username"]', username);
            await this.page.type('input[type="password"], input[name="password"], input[id="password"]', password);
            
            // Submit form
            await this.page.click('button[type="submit"], input[type="submit"], .login-button');
            
            // Wait for navigation after login
            await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });
            
            // Check if login was successful (adjust based on DAT's post-login page)
            const currentUrl = this.page.url();
            if (currentUrl.includes('dashboard') || currentUrl.includes('loadboard')) {
                this.isLoggedIn = true;
                console.log(`Login successful for user: ${this.userId}`);
                return { success: true, message: 'Login successful' };
            } else {
                console.log(`Login failed for user: ${this.userId}`);
                return { success: false, message: 'Login failed' };
            }
            
        } catch (error) {
            console.error(`Login error for user ${this.userId}:`, error);
            return { success: false, message: 'Login error: ' + error.message };
        }
    }

    async searchLoads(searchParams) {
        try {
            if (!this.isLoggedIn) {
                return { success: false, message: 'Not logged in' };
            }

            console.log(`Searching loads for user: ${this.userId}`, searchParams);
            
            // Navigate to load board if not already there
            if (!this.page.url().includes('loadboard')) {
                await this.page.goto('https://one.dat.com/loadboard', { 
                    waitUntil: 'networkidle2',
                    timeout: 30000 
                });
            }

            // Fill search form (adjust selectors based on actual DAT interface)
            if (searchParams.origin) {
                await this.page.waitForSelector('#origin, .origin-input, input[placeholder*="origin"]', { timeout: 5000 });
                await this.page.click('#origin, .origin-input, input[placeholder*="origin"]');
                await this.page.keyboard.selectAll();
                await this.page.type('#origin, .origin-input, input[placeholder*="origin"]', searchParams.origin);
            }

            if (searchParams.destination) {
                await this.page.waitForSelector('#destination, .destination-input, input[placeholder*="destination"]', { timeout: 5000 });
                await this.page.click('#destination, .destination-input, input[placeholder*="destination"]');
                await this.page.keyboard.selectAll();
                await this.page.type('#destination, .destination-input, input[placeholder*="destination"]', searchParams.destination);
            }

            // Submit search
            await this.page.click('.search-button, button[type="submit"], .btn-search');
            
            // Wait for results
            await this.page.waitForSelector('.load-results, .results-table, .load-list', { timeout: 10000 });
            
            // Extract load data
            const loads = await this.page.evaluate(() => {
                // This will need to be customized based on DAT's actual HTML structure
                const loadElements = document.querySelectorAll('.load-row, .load-item, tr.load');
                const results = [];
                
                loadElements.forEach((element, index) => {
                    if (index < 20) { // Limit to first 20 results
                        const loadData = {
                            id: element.getAttribute('data-id') || index,
                            origin: element.querySelector('.origin, .pickup')?.textContent?.trim() || '',
                            destination: element.querySelector('.destination, .delivery')?.textContent?.trim() || '',
                            rate: element.querySelector('.rate, .price')?.textContent?.trim() || '',
                            distance: element.querySelector('.distance, .miles')?.textContent?.trim() || '',
                            equipment: element.querySelector('.equipment, .trailer')?.textContent?.trim() || '',
                            company: element.querySelector('.company, .broker')?.textContent?.trim() || ''
                        };
                        results.push(loadData);
                    }
                });
                
                return results;
            });

            this.lastActivity = Date.now();
            
            console.log(`Found ${loads.length} loads for user: ${this.userId}`);
            return { success: true, loads: loads };
            
        } catch (error) {
            console.error(`Search error for user ${this.userId}:`, error);
            return { success: false, message: 'Search error: ' + error.message };
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
            console.log(`Browser closed for user: ${this.userId}`);
        }
    }

    updateActivity() {
        this.lastActivity = Date.now();
    }
}

// API Routes

// Get or create user session
app.get('/api/session/:userId', async (req, res) => {
    const userId = req.params.userId;
    
    try {
        if (!userSessions.has(userId)) {
            const session = new DATSession(userId);
            await session.initialize();
            userSessions.set(userId, session);
        }
        
        const session = userSessions.get(userId);
        session.updateActivity();
        
        res.json({ 
            success: true, 
            userId: userId,
            isLoggedIn: session.isLoggedIn 
        });
    } catch (error) {
        console.error('Session creation error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Login endpoint
app.post('/api/login/:userId', async (req, res) => {
    const userId = req.params.userId;
    const { username, password } = req.body;
    
    try {
        if (!userSessions.has(userId)) {
            const session = new DATSession(userId);
            await session.initialize();
            userSessions.set(userId, session);
        }
        
        const session = userSessions.get(userId);
        const result = await session.login(username, password);
        
        res.json(result);
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Search loads endpoint
app.post('/api/search/:userId', async (req, res) => {
    const userId = req.params.userId;
    const searchParams = req.body;
    
    try {
        const session = userSessions.get(userId);
        if (!session) {
            return res.status(404).json({ success: false, message: 'Session not found' });
        }
        
        const result = await session.searchLoads(searchParams);
        res.json(result);
    } catch (error) {
        console.error('Search error:', error);
        res.status(500).json({ success: false, message: error.message });
    }
});

// Cleanup inactive sessions every 30 minutes
setInterval(() => {
    const now = Date.now();
    const thirtyMinutes = 30 * 60 * 1000;
    
    for (const [userId, session] of userSessions.entries()) {
        if (now - session.lastActivity > thirtyMinutes) {
            console.log(`Cleaning up inactive session for user: ${userId}`);
            session.cleanup();
            userSessions.delete(userId);
        }
    }
}, 30 * 60 * 1000);

// Serve the main application
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`DAT Proxy Server running on port ${PORT}`);
    console.log(`Access the application at: http://localhost:${PORT}`);
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('Shutting down gracefully...');
    
    // Close all browser sessions
    for (const [userId, session] of userSessions.entries()) {
        await session.cleanup();
    }
    
    process.exit(0);
});
