<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct DAT iframe Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #ccc;
            margin: 10px 0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .success {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>🧪 Direct DAT iframe Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Direct DAT Dashboard</h2>
        <p>Trying to load: <code>https://one.dat.com/dashboard</code></p>
        <iframe src="https://one.dat.com/dashboard" 
                onload="handleLoad('test1')" 
                onerror="handleError('test1')">
        </iframe>
        <div id="test1-result">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: DAT Login Page</h2>
        <p>Trying to load: <code>https://login.dat.com</code></p>
        <iframe src="https://login.dat.com" 
                onload="handleLoad('test2')" 
                onerror="handleError('test2')">
        </iframe>
        <div id="test2-result">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: DAT Main Site</h2>
        <p>Trying to load: <code>https://www.dat.com</code></p>
        <iframe src="https://www.dat.com" 
                onload="handleLoad('test3')" 
                onerror="handleError('test3')">
        </iframe>
        <div id="test3-result">Loading...</div>
    </div>
    
    <div class="test-section">
        <h2>Control Test: Google (should work)</h2>
        <p>Trying to load: <code>https://www.google.com</code></p>
        <iframe src="https://www.google.com" 
                onload="handleLoad('control')" 
                onerror="handleError('control')">
        </iframe>
        <div id="control-result">Loading...</div>
    </div>

    <script>
        function handleLoad(testId) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = '<span class="success">✅ iframe loaded successfully!</span>';
            console.log(`${testId}: iframe loaded`);
        }
        
        function handleError(testId) {
            const resultDiv = document.getElementById(testId + '-result');
            resultDiv.innerHTML = '<span class="error">❌ iframe failed to load</span>';
            console.log(`${testId}: iframe error`);
        }
        
        // Check for X-Frame-Options blocking after a delay
        setTimeout(() => {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                const testIds = ['test1', 'test2', 'test3', 'control'];
                const testId = testIds[index];
                
                try {
                    // Try to access iframe content
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    if (!doc || doc.location.href === 'about:blank') {
                        const resultDiv = document.getElementById(testId + '-result');
                        if (resultDiv.innerHTML === 'Loading...') {
                            resultDiv.innerHTML = '<span class="error">❌ Blocked by X-Frame-Options or CSP</span>';
                        }
                    }
                } catch (e) {
                    console.log(`${testId}: Cross-origin access blocked (expected)`);
                }
            });
        }, 5000);
    </script>
</body>
</html>
