<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DAT Login Flow Debugger</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            display: flex;
            height: 100vh;
        }
        
        .sidebar {
            width: 400px;
            background: white;
            border-right: 2px solid #ddd;
            padding: 20px;
            overflow-y: auto;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .page-viewer {
            flex: 1;
            background: white;
            overflow: auto;
            border: 1px solid #ddd;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-info {
            background: #17a2b8;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .section h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .element-list {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .element-item {
            padding: 8px;
            margin: 4px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .element-item:hover {
            background: #e3f2fd;
        }
        
        .element-item.input {
            border-left: 4px solid #2196f3;
        }
        
        .element-item.button {
            border-left: 4px solid #4caf50;
        }
        
        .element-item.link {
            border-left: 4px solid #ff9800;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        
        .page-info {
            background: #e8f5e8;
            padding: 10px;
            margin-bottom: 15px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="sidebar">
        <h2>DAT Login Debugger</h2>
        
        <div id="status" class="status info">Ready to start</div>
        
        <div class="section">
            <h3>Navigation</h3>
            <button class="btn btn-success" onclick="testSimple()">Test Simple Page</button>
            <button class="btn btn-info" onclick="testGoogle()">Test Google</button>
            <button class="btn btn-primary" onclick="navigateToDAT()">Go to DAT Dashboard</button>
            <button class="btn btn-warning" onclick="refreshPage()">Refresh Page State</button>
            <button class="btn btn-success" onclick="takeScreenshot()">Take Screenshot</button>
            <button class="btn btn-info" onclick="debugButtons()">Debug Buttons</button>
            <button id="autoRefreshBtn" class="btn btn-success" onclick="toggleAutoRefresh()">Enable Auto-Refresh</button>
        </div>
        
        <div class="section">
            <h3>Manual Actions</h3>
            <div class="form-group">
                <label>Type in Field:</label>
                <input type="text" id="typeSelector" placeholder="CSS Selector (e.g., #username)">
                <input type="text" id="typeText" placeholder="Text to type">
                <button class="btn btn-primary" onclick="typeInField()">Type</button>
            </div>
            
            <div class="form-group">
                <label>Click Element:</label>
                <input type="text" id="clickSelector" placeholder="CSS Selector">
                <button class="btn btn-primary" onclick="clickElement()">Click</button>
            </div>
        </div>
        
        <div id="pageInfo" class="page-info" style="display: none;">
            <strong>Current Page:</strong><br>
            <span id="currentUrl">-</span><br>
            <strong>Title:</strong> <span id="currentTitle">-</span><br>
            <strong>Step:</strong> <span id="currentStep">-</span>
        </div>
        
        <div class="section">
            <h3>Input Fields</h3>
            <div id="inputFields" class="element-list">
                <div class="loading">No page loaded</div>
            </div>
        </div>
        
        <div class="section">
            <h3>Buttons</h3>
            <div id="buttons" class="element-list">
                <div class="loading">No page loaded</div>
            </div>
        </div>
        
        <div class="section">
            <h3>Links</h3>
            <div id="links" class="element-list">
                <div class="loading">No page loaded</div>
            </div>
        </div>
    </div>
    
    <div class="main-content">
        <div class="toolbar">
            <span>Page Viewer</span>
            <button class="btn btn-primary" onclick="viewSource()">View Source</button>
            <button class="btn btn-success" onclick="viewRendered()">View Rendered</button>
        </div>
        
        <div class="page-viewer">
            <iframe id="pageFrame" src="about:blank"></iframe>
        </div>
    </div>

    <script>
        let currentPageState = null;
        let viewMode = 'rendered'; // 'rendered' or 'source'
        
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        async function testSimple() {
            showStatus('Testing with simple page...', 'info');

            try {
                const response = await fetch('/api/test-simple', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success !== false) {
                    showStatus('Simple test complete!', 'success');
                    updatePageState(result);
                } else {
                    showStatus('Test failed: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }

        async function testGoogle() {
            showStatus('Testing with Google...', 'info');

            try {
                const response = await fetch('/api/test-google', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success !== false) {
                    showStatus('Google test complete!', 'success');
                    updatePageState(result);
                } else {
                    showStatus('Google test failed: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }

        async function navigateToDAT() {
            showStatus('Navigating to DAT...', 'info');

            try {
                const response = await fetch('/api/navigate', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ url: 'https://one.dat.com/dashboard' })
                });

                const result = await response.json();

                if (result.success !== false) {
                    showStatus('Navigation complete!', 'success');
                    updatePageState(result);
                } else {
                    showStatus('Navigation failed: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }
        
        async function refreshPage() {
            showStatus('Refreshing page state...', 'info');
            
            try {
                const response = await fetch('/api/status');
                const result = await response.json();
                
                if (result.success !== false) {
                    showStatus('Page state updated!', 'success');
                    updatePageState(result);
                } else {
                    showStatus('Failed to get page state: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }
        
        async function typeInField() {
            const selector = document.getElementById('typeSelector').value;
            const text = document.getElementById('typeText').value;

            if (!selector || !text) {
                showStatus('Please enter both selector and text', 'error');
                return;
            }

            // Temporarily disable auto-refresh during typing
            const wasAutoRefreshEnabled = autoRefreshEnabled;
            if (autoRefreshEnabled) {
                toggleAutoRefresh();
            }

            showStatus(`Typing "${text}" in ${selector}...`, 'info');

            try {
                const response = await fetch('/api/type', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ selector, text })
                });

                const result = await response.json();

                if (result.success !== false) {
                    showStatus('Text entered successfully! (Auto-refresh paused)', 'success');
                    updatePageState(result);
                    document.getElementById('typeText').value = '';
                } else {
                    showStatus('Failed to type: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }

            // Note: Don't re-enable auto-refresh automatically to prevent interference
        }
        
        async function clickElement() {
            const selector = document.getElementById('clickSelector').value;
            
            if (!selector) {
                showStatus('Please enter a selector', 'error');
                return;
            }
            
            showStatus(`Clicking ${selector}...`, 'info');
            
            try {
                const response = await fetch('/api/click', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ selector })
                });
                
                const result = await response.json();
                
                if (result.success !== false) {
                    showStatus('Element clicked successfully!', 'success');
                    updatePageState(result);
                    document.getElementById('clickSelector').value = '';
                } else {
                    showStatus('Failed to click: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }
        
        async function takeScreenshot() {
            showStatus('Taking screenshot...', 'info');
            
            try {
                const response = await fetch('/api/screenshot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus(`Screenshot saved: ${result.filename}`, 'success');
                    // Open screenshot in new tab
                    window.open(`/${result.filename}`, '_blank');
                } else {
                    showStatus('Failed to take screenshot: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }

        async function debugButtons() {
            showStatus('Analyzing buttons on current page...', 'info');

            try {
                const response = await fetch('/api/debug-buttons', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const result = await response.json();

                if (result.success) {
                    showStatus(`Found ${result.count} clickable elements - check console for details`, 'success');
                    console.log('🔍 Available buttons:', result.buttons);

                    // Show buttons in a more readable format
                    const buttonInfo = result.buttons.map(btn =>
                        `${btn.index}. "${btn.text}" - ${btn.selector} ${btn.visible ? '✅' : '❌hidden'} ${btn.disabled ? '🚫disabled' : ''}`
                    ).join('\n');

                    alert(`Found ${result.count} clickable elements:\n\n${buttonInfo}`);
                } else {
                    showStatus('Debug failed: ' + result.error, 'error');
                }
            } catch (error) {
                showStatus('Error: ' + error.message, 'error');
            }
        }

        function updatePageState(state) {
            currentPageState = state;
            
            // Update page info
            document.getElementById('pageInfo').style.display = 'block';
            document.getElementById('currentUrl').textContent = state.url || '-';
            document.getElementById('currentTitle').textContent = state.title || '-';
            document.getElementById('currentStep').textContent = state.step || '-';
            
            // Update elements lists
            updateElementsList('inputFields', state.elements?.inputs || [], 'input');
            updateElementsList('buttons', state.elements?.buttons || [], 'button');
            updateElementsList('links', state.elements?.links || [], 'link');
            
            // Update page viewer
            if (viewMode === 'rendered') {
                viewRendered();
            } else {
                viewSource();
            }
        }
        
        function updateElementsList(containerId, elements, type) {
            const container = document.getElementById(containerId);
            
            if (elements.length === 0) {
                container.innerHTML = '<div class="loading">No elements found</div>';
                return;
            }
            
            container.innerHTML = elements.map((el, index) => {
                let text = '';
                if (type === 'input') {
                    text = `${el.inputType} - ${el.placeholder || el.name || el.id || 'unnamed'}`;
                } else if (type === 'button') {
                    text = el.text || 'unnamed button';
                } else if (type === 'link') {
                    text = el.text;
                }
                
                return `
                    <div class="element-item ${type}" onclick="selectElement('${el.selector}', '${type}')">
                        <strong>${text}</strong><br>
                        <small>Selector: ${el.selector}</small>
                    </div>
                `;
            }).join('');
        }
        
        function selectElement(selector, type) {
            if (type === 'input') {
                document.getElementById('typeSelector').value = selector;
            } else {
                document.getElementById('clickSelector').value = selector;
            }
        }
        
        function viewSource() {
            viewMode = 'source';
            if (currentPageState && currentPageState.html) {
                const frame = document.getElementById('pageFrame');
                const blob = new Blob([`<pre style="white-space: pre-wrap; font-family: monospace; padding: 20px;">${escapeHtml(currentPageState.html)}</pre>`], {type: 'text/html'});
                frame.src = URL.createObjectURL(blob);
            }
        }
        
        function viewRendered() {
            viewMode = 'rendered';
            if (currentPageState && currentPageState.html) {
                const frame = document.getElementById('pageFrame');

                // Create interaction capture script
                const interactionScript = createInteractionScript();

                // Inject the script into the HTML
                let modifiedHtml = currentPageState.html;

                // Try to inject before </body>, fallback to end of HTML
                if (modifiedHtml.toLowerCase().includes('</body>')) {
                    modifiedHtml = modifiedHtml.replace(/<\/body>/i, interactionScript + '</body>');
                } else if (modifiedHtml.toLowerCase().includes('</html>')) {
                    modifiedHtml = modifiedHtml.replace(/<\/html>/i, interactionScript + '</html>');
                } else {
                    modifiedHtml = modifiedHtml + interactionScript;
                }

                const blob = new Blob([modifiedHtml], {type: 'text/html'});
                frame.src = URL.createObjectURL(blob);
            }
        }

        function createInteractionScript() {
            var script = '<script>';
            script += '(function() {';
            script += 'console.log("Interaction capture script loaded");';
            script += 'function getSelector(element) {';
            script += 'if (element.id) return "#" + element.id;';
            script += 'if (element.name) return element.tagName.toLowerCase() + "[name=\\"" + element.name + "\\"]";';
            script += 'if (element.className) {';
            script += 'var classes = element.className.split(" ").filter(function(c) { return c.length > 0; });';
            script += 'if (classes.length > 0) return element.tagName.toLowerCase() + "." + classes[0];';
            script += '}';
            script += 'var parent = element.parentElement;';
            script += 'if (parent) {';
            script += 'var siblings = Array.from(parent.children).filter(function(child) { return child.tagName === element.tagName; });';
            script += 'var index = siblings.indexOf(element) + 1;';
            script += 'return element.tagName.toLowerCase() + ":nth-of-type(" + index + ")";';
            script += '}';
            script += 'return element.tagName.toLowerCase();';
            script += '}';
            script += 'var inputTimeout = null;';
            script += 'document.addEventListener("input", function(e) {';
            script += 'if (e.target.tagName === "INPUT" || e.target.tagName === "TEXTAREA") {';
            script += 'clearTimeout(inputTimeout);';
            script += 'inputTimeout = setTimeout(function() {';
            script += 'var selector = getSelector(e.target);';
            script += 'var value = e.target.value;';
            script += 'console.log("Input detected:", selector, value);';
            script += 'try {';
            script += 'window.parent.postMessage({';
            script += 'type: "INPUT_DETECTED",';
            script += 'selector: selector,';
            script += 'value: value,';
            script += 'fieldType: e.target.type,';
            script += 'placeholder: e.target.placeholder,';
            script += 'name: e.target.name,';
            script += 'id: e.target.id';
            script += '}, "*");';
            script += '} catch (err) {';
            script += 'console.error("Failed to send input message:", err);';
            script += '}';
            script += '}, 300);';
            script += '}';
            script += '});';
            script += 'document.addEventListener("click", function(e) {';
            script += 'console.log("Click event on:", e.target.tagName, e.target.type, e.target);';
            script += 'var isClickable = e.target.tagName === "BUTTON" || (e.target.tagName === "INPUT" && (e.target.type === "submit" || e.target.type === "button")) || e.target.tagName === "A";';
            script += 'if (isClickable) {';
            script += 'console.log("Clickable element detected, preventing default and capturing...");';
            script += 'e.preventDefault();';
            script += 'var selector = getSelector(e.target);';
            script += 'var text = e.target.textContent || e.target.value || e.target.innerText;';
            script += 'console.log("Click detected:", selector, text);';
            script += 'try {';
            script += 'window.parent.postMessage({';
            script += 'type: "CLICK_DETECTED",';
            script += 'selector: selector,';
            script += 'text: text,';
            script += 'tagName: e.target.tagName,';
            script += 'elementType: e.target.type,';
            script += 'href: e.target.href,';
            script += 'id: e.target.id,';
            script += 'className: e.target.className';
            script += '}, "*");';
            script += '} catch (err) {';
            script += 'console.error("Failed to send click message:", err);';
            script += '}';
            script += '} else {';
            script += 'console.log("Non-clickable element clicked:", e.target.tagName, e.target.type);';
            script += '}';
            script += '});';
            script += 'var style = document.createElement("style");';
            script += 'style.textContent = "input, textarea, button, a { outline: 2px dashed #007bff !important; outline-offset: 2px !important; } input:focus, textarea:focus, button:hover, a:hover { outline: 3px solid #28a745 !important; outline-offset: 2px !important; }";';
            script += 'document.head.appendChild(style);';
            script += 'console.log("Interaction capture ready");';
            script += '})();';
            script += '</' + 'script>';
            return script;
        }
        
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        let autoRefreshEnabled = true;
        let autoRefreshInterval = null;

        function toggleAutoRefresh() {
            autoRefreshEnabled = !autoRefreshEnabled;
            const btn = document.getElementById('autoRefreshBtn');

            if (autoRefreshEnabled) {
                btn.textContent = 'Disable Auto-Refresh';
                btn.className = 'btn btn-warning';
                autoRefreshInterval = setInterval(refreshPage, 10000); // Increased to 10 seconds
                showStatus('Auto-refresh enabled (10s)', 'info');
            } else {
                btn.textContent = 'Enable Auto-Refresh';
                btn.className = 'btn btn-success';
                if (autoRefreshInterval) {
                    clearInterval(autoRefreshInterval);
                    autoRefreshInterval = null;
                }
                showStatus('Auto-refresh disabled', 'info');
            }
        }

        // Listen for messages from the iframe
        window.addEventListener('message', function(event) {
            console.log('📨 Message received:', event.data);

            if (event.data.type === 'INPUT_DETECTED') {
                const data = event.data;
                showStatus(`🎯 Real-time typing detected: "${data.value}" in ${data.selector}`, 'info');

                // Auto-fill the manual input fields for visibility
                document.getElementById('typeSelector').value = data.selector;
                document.getElementById('typeText').value = data.value;

                // Automatically send to backend with minimal delay
                setTimeout(async () => {
                    console.log('🚀 Auto-sending input to backend:', data.selector, data.value);
                    try {
                        const response = await fetch('/api/type', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ selector: data.selector, text: data.value })
                        });
                        const result = await response.json();
                        if (result.success !== false) {
                            console.log('✅ Input sent successfully');
                        } else {
                            console.error('❌ Input failed:', result.error);
                            showStatus('Failed to send input: ' + result.error, 'error');
                        }
                    } catch (error) {
                        console.error('❌ Input error:', error);
                        showStatus('Error sending input: ' + error.message, 'error');
                    }
                }, 50);

            } else if (event.data.type === 'CLICK_DETECTED') {
                const data = event.data;
                showStatus(`🎯 Real-time click detected: "${data.text}" (${data.selector})`, 'info');

                // Auto-fill the click selector for visibility
                document.getElementById('clickSelector').value = data.selector;

                // Automatically send to backend with minimal delay
                setTimeout(async () => {
                    console.log('🚀 Auto-sending click to backend:', data.selector);
                    try {
                        const response = await fetch('/api/click', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ selector: data.selector })
                        });
                        const result = await response.json();
                        if (result.success !== false) {
                            console.log('✅ Click sent successfully');
                            showStatus('✅ Click sent to backend successfully', 'success');
                            // Update page state after click
                            updatePageState(result);
                        } else {
                            console.error('❌ Click failed:', result.error);
                            showStatus('Failed to send click: ' + result.error, 'error');
                        }
                    } catch (error) {
                        console.error('❌ Click error:', error);
                        showStatus('Error sending click: ' + error.message, 'error');
                    }
                }, 50);
            }
        });

        // Start with auto-refresh disabled by default
        autoRefreshEnabled = false;

        // Initial load
        refreshPage();
    </script>
</body>
</html>
