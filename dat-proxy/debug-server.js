const express = require('express');
const puppeteer = require('puppeteer');
const path = require('path');

const app = express();
const PORT = 3001;

app.use(express.json());
app.use(express.static('debug-public'));

let browser = null;
let page = null;
let currentStep = 'initial';

class DATDebugger {
    constructor() {
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;

        console.log('🚀 Initializing browser...');

        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        page = await browser.newPage();

        // Add console logging from the page
        page.on('console', msg => {
            console.log(`🌐 Page console: ${msg.text()}`);
        });

        page.on('pageerror', error => {
            console.log(`🚨 Page error: ${error.message}`);
        });

        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await page.setViewport({ width: 1920, height: 1080 });

        // Navigate to a blank page initially
        await page.goto('about:blank');
        console.log(`📍 Initial page URL: ${page.url()}`);

        this.initialized = true;
        console.log('✅ Browser initialized');
    }

    async getPageState() {
        if (!page) return { error: 'Browser not initialized' };

        try {
            // Check if page is still valid
            if (page.isClosed()) {
                return { error: 'Page is closed' };
            }

            // Wait a bit for any ongoing navigation to complete
            await new Promise(resolve => setTimeout(resolve, 500));

            const url = page.url();
            const title = await page.title();

            // Get HTML content with timeout
            const html = await Promise.race([
                page.content(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Content timeout')), 5000))
            ]);

            // Get all interactive elements with error handling
            let elements = { inputs: [], buttons: [], links: [] };

            try {
                elements = await Promise.race([
                    page.evaluate(() => {
                        const inputs = Array.from(document.querySelectorAll('input')).map((el, index) => ({
                            type: 'input',
                            index,
                            inputType: el.type,
                            name: el.name,
                            id: el.id,
                            placeholder: el.placeholder,
                            value: el.value,
                            className: el.className,
                            selector: el.id ? `#${el.id}` : el.name ? `input[name="${el.name}"]` : `input:nth-of-type(${index + 1})`
                        }));

                        const buttons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]')).map((el, index) => ({
                            type: 'button',
                            index,
                            text: el.textContent?.trim() || el.value,
                            className: el.className,
                            id: el.id,
                            selector: el.id ? `#${el.id}` : `button:nth-of-type(${index + 1})`
                        }));

                        const links = Array.from(document.querySelectorAll('a')).map((el, index) => ({
                            type: 'link',
                            index,
                            text: el.textContent?.trim(),
                            href: el.href,
                            className: el.className,
                            id: el.id,
                            selector: el.id ? `#${el.id}` : `a:nth-of-type(${index + 1})`
                        })).filter(link => link.text && link.text.length > 0);

                        return { inputs, buttons, links };
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Elements timeout')), 3000))
                ]);
            } catch (evalError) {
                console.log('Warning: Could not evaluate page elements:', evalError.message);
                // Continue with empty elements
            }

            return {
                success: true,
                url,
                title,
                html,
                elements,
                step: currentStep
            };

        } catch (error) {
            console.error('Page state error:', error.message);

            // If it's a detached frame error, try to reinitialize
            if (error.message.includes('detached Frame') || error.message.includes('Target closed')) {
                console.log('🔄 Reinitializing browser due to detached frame...');
                try {
                    await this.reinitialize();
                    return await this.getPageState(); // Retry once
                } catch (reinitError) {
                    return { success: false, error: 'Browser reinitialization failed: ' + reinitError.message };
                }
            }

            return { success: false, error: error.message };
        }
    }

    async reinitialize() {
        console.log('🔄 Reinitializing browser...');

        // Close existing browser if it exists
        if (browser && !browser.process()?.killed) {
            try {
                await browser.close();
            } catch (e) {
                console.log('Warning: Error closing browser:', e.message);
            }
        }

        // Reset state
        browser = null;
        page = null;
        this.initialized = false;

        // Reinitialize
        await this.initialize();
    }
}

const datDebugger = new DATDebugger();

// Initialize on startup
datDebugger.initialize();

// API Routes
app.get('/api/status', async (req, res) => {
    const state = await datDebugger.getPageState();
    res.json(state);
});

// Simple test endpoint
app.post('/api/test-google', async (req, res) => {
    try {
        console.log('🧪 Testing with Google...');
        currentStep = 'testing_google';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        await page.goto('https://www.google.com', {
            waitUntil: 'domcontentloaded',
            timeout: 15000
        });

        currentStep = 'google_loaded';
        console.log('✅ Google loaded successfully');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Google test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

// Test endpoint for simple navigation
app.post('/api/test', async (req, res) => {
    try {
        console.log('🧪 Testing with simple page...');
        currentStep = 'testing';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        await page.goto('https://httpbin.org/html', {
            waitUntil: 'domcontentloaded',
            timeout: 15000
        });

        currentStep = 'test_complete';
        console.log('✅ Test navigation complete');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/navigate', async (req, res) => {
    const { url } = req.body;

    try {
        console.log(`🌐 Navigating to: ${url || 'https://one.dat.com/dashboard'}`);
        currentStep = 'navigating';

        // Ensure we have a valid page
        if (!page || page.isClosed()) {
            console.log('🔄 Page not available, reinitializing...');
            await datDebugger.reinitialize();
        }

        // Add request/response logging
        page.on('response', response => {
            const status = response.status();
            const url = response.url();
            if (status >= 400) {
                console.log(`❌ Response error: ${status} ${url}`);
            } else {
                console.log(`📥 Response: ${status} ${url}`);
            }
        });

        page.on('requestfailed', request => {
            console.log(`❌ Request failed: ${request.url()} - ${request.failure()?.errorText || 'Unknown error'}`);
        });

        page.on('error', error => {
            console.log(`🚨 Page error: ${error.message}`);
        });

        const targetUrl = url || 'https://one.dat.com/dashboard';
        console.log(`🎯 Attempting to load: ${targetUrl}`);

        const response = await page.goto(targetUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        console.log(`📊 Response status: ${response.status()}`);
        console.log(`📊 Response headers:`, response.headers());

        // Wait a bit for any redirects or dynamic content
        await new Promise(resolve => setTimeout(resolve, 2000));

        currentStep = 'page_loaded';
        const finalUrl = page.url();
        console.log(`✅ Navigation complete. Final URL: ${finalUrl}`);

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Navigation error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/type', async (req, res) => {
    const { selector, text } = req.body;

    try {
        console.log(`⌨️  Typing "${text}" in: ${selector}`);
        currentStep = 'typing';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        // Wait for element and ensure it's visible
        await page.waitForSelector(selector, { timeout: 5000, visible: true });

        // Focus the element
        await page.focus(selector);

        // Clear existing content
        await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            if (element) {
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }, selector);

        // Type the text with human-like delays
        await page.type(selector, text, { delay: 50 });

        // Trigger change event
        await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            if (element) {
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }, selector);

        currentStep = 'typed';
        console.log('✅ Text entered successfully');

        // Don't automatically refresh page state to avoid clearing the input
        res.json({
            success: true,
            message: 'Text entered successfully',
            selector,
            text,
            step: currentStep
        });

    } catch (error) {
        console.error('❌ Typing error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/click', async (req, res) => {
    const { selector } = req.body;

    try {
        console.log(`👆 Clicking: ${selector}`);
        currentStep = 'clicking';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        let actualSelector = selector;

        // Handle text-based selectors
        if (selector.includes('[text="')) {
            const textMatch = selector.match(/\[text="([^"]+)"\]/);
            if (textMatch) {
                const text = textMatch[1];
                const tagName = selector.split('[')[0];

                console.log(`🔍 Looking for ${tagName} with text: "${text}"`);

                // Find element by text content
                actualSelector = await page.evaluate((tag, txt) => {
                    const elements = document.querySelectorAll(tag);
                    for (let el of elements) {
                        const elementText = el.textContent?.trim() || el.value || '';
                        if (elementText === txt) {
                            // Generate a more specific selector for this element
                            if (el.id) return `#${el.id}`;
                            if (el.name) return `${tag}[name="${el.name}"]`;
                            if (el.className) {
                                const classes = el.className.split(' ').filter(c => c.length > 0);
                                if (classes.length > 0) return `${tag}.${classes[0]}`;
                            }
                            // Fallback to nth-of-type
                            const siblings = Array.from(document.querySelectorAll(tag));
                            const index = siblings.indexOf(el) + 1;
                            return `${tag}:nth-of-type(${index})`;
                        }
                    }
                    return null;
                }, tagName, text);

                if (!actualSelector) {
                    throw new Error(`Could not find ${tagName} with text "${text}"`);
                }

                console.log(`🎯 Found element with selector: ${actualSelector}`);
            }
        }

        await page.waitForSelector(actualSelector, { timeout: 5000 });
        await page.click(actualSelector);

        // Wait for any changes
        await new Promise(resolve => setTimeout(resolve, 2000));

        currentStep = 'clicked';
        console.log('✅ Element clicked');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Click error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/screenshot', async (req, res) => {
    try {
        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        // Get current page info before screenshot
        const url = page.url();
        const title = await page.title();
        console.log(`📸 Taking screenshot of: ${url} (${title})`);

        const filename = `screenshot-${Date.now()}.png`;
        await page.screenshot({
            path: `debug-public/${filename}`,
            fullPage: true
        });

        console.log(`📸 Screenshot saved: ${filename}`);
        res.json({ success: true, filename, url, title });

    } catch (error) {
        console.error('❌ Screenshot error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Debug endpoint to get all buttons on current page
app.post('/api/debug-buttons', async (req, res) => {
    try {
        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        console.log('🔍 Analyzing all buttons on current page...');

        const buttons = await page.evaluate(() => {
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');

            function generateBestSelector(element) {
                // Priority 1: ID
                if (element.id) {
                    return `#${element.id}`;
                }

                // Priority 2: Name attribute
                if (element.name) {
                    return `${element.tagName.toLowerCase()}[name="${element.name}"]`;
                }

                // Priority 3: Text content (for buttons with unique text)
                const text = element.textContent?.trim() || element.value || '';
                if (text && text.length > 0 && text.length < 50) {
                    // Check if text is unique on the page
                    const sameTextElements = document.querySelectorAll(`${element.tagName.toLowerCase()}`);
                    const uniqueText = Array.from(sameTextElements).filter(el =>
                        (el.textContent?.trim() || el.value || '') === text
                    ).length === 1;

                    if (uniqueText) {
                        return `${element.tagName.toLowerCase()}[text="${text}"]`;
                    }
                }

                // Priority 4: Class-based selector (more specific)
                if (element.className) {
                    const classes = element.className.split(' ').filter(c => c.length > 0);
                    if (classes.length > 0) {
                        // Try with first class
                        const classSelector = `${element.tagName.toLowerCase()}.${classes[0]}`;
                        const sameClassElements = document.querySelectorAll(classSelector);
                        if (sameClassElements.length === 1) {
                            return classSelector;
                        }

                        // Try with multiple classes if needed
                        if (classes.length > 1) {
                            const multiClassSelector = `${element.tagName.toLowerCase()}.${classes.slice(0, 2).join('.')}`;
                            const multiClassElements = document.querySelectorAll(multiClassSelector);
                            if (multiClassElements.length === 1) {
                                return multiClassSelector;
                            }
                        }
                    }
                }

                // Priority 5: Parent-based selector
                const parent = element.parentElement;
                if (parent) {
                    const parentSelector = parent.id ? `#${parent.id}` :
                                         parent.className ? `.${parent.className.split(' ')[0]}` :
                                         parent.tagName.toLowerCase();

                    const childButtons = parent.querySelectorAll(element.tagName.toLowerCase());
                    const childIndex = Array.from(childButtons).indexOf(element) + 1;

                    if (childButtons.length > 1) {
                        return `${parentSelector} > ${element.tagName.toLowerCase()}:nth-child(${childIndex})`;
                    }
                }

                // Fallback: nth-of-type (least reliable)
                const siblings = Array.from(document.querySelectorAll(element.tagName.toLowerCase()));
                const index = siblings.indexOf(element) + 1;
                return `${element.tagName.toLowerCase()}:nth-of-type(${index})`;
            }

            return Array.from(allButtons).map((btn, index) => ({
                index: index + 1,
                tagName: btn.tagName,
                type: btn.type,
                text: btn.textContent?.trim() || btn.value || '',
                id: btn.id,
                name: btn.name,
                className: btn.className,
                selector: generateBestSelector(btn),
                visible: btn.offsetParent !== null,
                disabled: btn.disabled,
                rect: btn.getBoundingClientRect()
            }));
        });

        console.log(`🔍 Found ${buttons.length} clickable elements:`);
        buttons.forEach(btn => {
            console.log(`  ${btn.index}. ${btn.tagName} "${btn.text}" - ${btn.selector} (visible: ${btn.visible}, disabled: ${btn.disabled})`);
        });

        res.json({ success: true, buttons, count: buttons.length });

    } catch (error) {
        console.error('❌ Debug buttons error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Test endpoint with simple page
app.post('/api/test-simple', async (req, res) => {
    try {
        console.log('🧪 Testing with simple page...');
        currentStep = 'testing';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        // Try a very simple page first
        await page.goto('data:text/html,<html><body><h1>Test Page</h1><input id="test" placeholder="Test input"><button>Test Button</button></body></html>', {
            waitUntil: 'domcontentloaded',
            timeout: 5000
        });

        currentStep = 'test_complete';
        console.log('✅ Simple test complete');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

// Serve the debug interface
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'debug-public', 'index.html'));
});

const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`🎯 DAT Debug Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log('🚀 Ready to debug DAT login flow!');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use. Please kill the existing process or use a different port.`);
        process.exit(1);
    } else {
        console.error('❌ Server error:', err);
        process.exit(1);
    }
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🧹 Shutting down...');
    if (browser) {
        await browser.close();
    }
    process.exit(0);
});
