const express = require('express');
const puppeteer = require('puppeteer');
const path = require('path');

const app = express();
const PORT = 3001;

app.use(express.json());
app.use(express.static('debug-public'));

let browser = null;
let page = null;
let currentStep = 'initial';

class DATDebugger {
    constructor() {
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;
        
        console.log('🚀 Initializing browser...');
        
        browser = await puppeteer.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled'
            ]
        });

        page = await browser.newPage();
        
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await page.setViewport({ width: 1920, height: 1080 });
        
        this.initialized = true;
        console.log('✅ Browser initialized');
    }

    async getPageState() {
        if (!page) return { error: 'Browser not initialized' };
        
        try {
            const url = page.url();
            const title = await page.title();
            const html = await page.content();
            
            // Get all interactive elements
            const elements = await page.evaluate(() => {
                const inputs = Array.from(document.querySelectorAll('input')).map((el, index) => ({
                    type: 'input',
                    index,
                    inputType: el.type,
                    name: el.name,
                    id: el.id,
                    placeholder: el.placeholder,
                    value: el.value,
                    className: el.className,
                    selector: el.id ? `#${el.id}` : el.name ? `input[name="${el.name}"]` : `input:nth-of-type(${index + 1})`
                }));
                
                const buttons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]')).map((el, index) => ({
                    type: 'button',
                    index,
                    text: el.textContent?.trim() || el.value,
                    className: el.className,
                    id: el.id,
                    selector: el.id ? `#${el.id}` : `button:nth-of-type(${index + 1})`
                }));
                
                const links = Array.from(document.querySelectorAll('a')).map((el, index) => ({
                    type: 'link',
                    index,
                    text: el.textContent?.trim(),
                    href: el.href,
                    className: el.className,
                    id: el.id,
                    selector: el.id ? `#${el.id}` : `a:nth-of-type(${index + 1})`
                })).filter(link => link.text && link.text.length > 0);
                
                return { inputs, buttons, links };
            });
            
            return {
                success: true,
                url,
                title,
                html,
                elements,
                step: currentStep
            };
            
        } catch (error) {
            return { success: false, error: error.message };
        }
    }
}

const datDebugger = new DATDebugger();

// Initialize on startup
datDebugger.initialize();

// API Routes
app.get('/api/status', async (req, res) => {
    const state = await datDebugger.getPageState();
    res.json(state);
});

app.post('/api/navigate', async (req, res) => {
    const { url } = req.body;
    
    try {
        console.log(`🌐 Navigating to: ${url}`);
        currentStep = 'navigating';
        
        await page.goto(url || 'https://one.dat.com/dashboard', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        currentStep = 'page_loaded';
        console.log('✅ Navigation complete');
        
        const state = await datDebugger.getPageState();
        res.json(state);
        
    } catch (error) {
        console.error('❌ Navigation error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/type', async (req, res) => {
    const { selector, text } = req.body;
    
    try {
        console.log(`⌨️  Typing "${text}" in: ${selector}`);
        currentStep = 'typing';
        
        await page.waitForSelector(selector, { timeout: 5000 });
        await page.click(selector);
        await page.keyboard.selectAll();
        await page.type(selector, text);
        
        currentStep = 'typed';
        console.log('✅ Text entered');
        
        const state = await datDebugger.getPageState();
        res.json(state);
        
    } catch (error) {
        console.error('❌ Typing error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/click', async (req, res) => {
    const { selector } = req.body;
    
    try {
        console.log(`👆 Clicking: ${selector}`);
        currentStep = 'clicking';
        
        await page.waitForSelector(selector, { timeout: 5000 });
        await page.click(selector);
        
        // Wait for any changes
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        currentStep = 'clicked';
        console.log('✅ Element clicked');
        
        const state = await datDebugger.getPageState();
        res.json(state);
        
    } catch (error) {
        console.error('❌ Click error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/screenshot', async (req, res) => {
    try {
        const filename = `screenshot-${Date.now()}.png`;
        await page.screenshot({ 
            path: `debug-public/${filename}`, 
            fullPage: true 
        });
        
        console.log(`📸 Screenshot saved: ${filename}`);
        res.json({ success: true, filename });
        
    } catch (error) {
        console.error('❌ Screenshot error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Serve the debug interface
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'debug-public', 'index.html'));
});

app.listen(PORT, () => {
    console.log(`🎯 DAT Debug Server running on port ${PORT}`);
    console.log(`🌐 Open: http://147.93.146.10:${PORT}`);
    console.log('🚀 Ready to debug DAT login flow!');
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🧹 Shutting down...');
    if (browser) {
        await browser.close();
    }
    process.exit(0);
});
