const express = require('express');
const puppeteer = require('puppeteer');
const path = require('path');

const app = express();
const PORT = 3001;

app.use(express.json());
app.use(express.static('debug-public'));

// Simple endpoint to serve current page content as iframe
app.get('/current-page', async (req, res) => {
    try {
        console.log('🖼️ Serving current page for iframe...');

        if (!page || page.isClosed()) {
            return res.status(500).send(`
                <html><body>
                    <h1>Browser Not Ready</h1>
                    <p>Please navigate to a page first using the control panel.</p>
                </body></html>
            `);
        }

        // Get the current page content
        let content = await page.content();
        const currentUrl = page.url();

        console.log(`📄 Serving content from: ${currentUrl}`);

        // Add base tag and prevent frame-busting
        content = content.replace(/<head>/i, `<head>
            <base href="https://one.dat.com/">
            <script>
                // Prevent frame-busting
                try {
                    Object.defineProperty(window, 'top', {
                        get: function() { return window; }
                    });
                    Object.defineProperty(window, 'parent', {
                        get: function() { return window; }
                    });
                } catch(e) {}

                console.log('🎯 DAT content loaded in iframe');
            </script>
        `);

        // Set headers to allow iframe embedding
        res.setHeader('Content-Type', 'text/html');
        res.setHeader('X-Frame-Options', 'ALLOWALL');
        res.setHeader('Content-Security-Policy', '');

        res.send(content);

    } catch (error) {
        console.error('❌ Current page error:', error);
        res.status(500).send(`
            <html><body>
                <h1>Error</h1>
                <p>Error loading current page: ${error.message}</p>
            </body></html>
        `);
    }
});

let browser = null;
let page = null;
let currentStep = 'initial';

class DATDebugger {
    constructor() {
        this.initialized = false;
    }

    async initialize() {
        if (this.initialized) return;

        console.log('🚀 Initializing browser...');

        browser = await puppeteer.launch({
            headless: true,
            userDataDir: './browser-data', // Persistent session storage
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        });

        page = await browser.newPage();

        // Add console logging from the page
        page.on('console', msg => {
            console.log(`🌐 Page console: ${msg.text()}`);
        });

        page.on('pageerror', error => {
            console.log(`🚨 Page error: ${error.message}`);
        });

        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        await page.setViewport({ width: 1920, height: 1080 });

        // Check for existing session
        console.log('🔍 Checking for existing session...');
        const cookies = await page.cookies();
        console.log(`🍪 Found ${cookies.length} existing cookies`);

        if (cookies.length > 0) {
            console.log('✅ Existing session detected - cookies found');
            // Log some cookie info (without sensitive data)
            cookies.forEach(cookie => {
                console.log(`  🍪 ${cookie.name}: ${cookie.domain} (${cookie.secure ? 'secure' : 'insecure'})`);
            });
        } else {
            console.log('🆕 No existing session - fresh browser');
        }

        // Navigate to a blank page initially
        await page.goto('about:blank');
        console.log(`📍 Initial page URL: ${page.url()}`);

        this.initialized = true;
        console.log('✅ Browser initialized');
    }

    async getPageState() {
        if (!page) return { error: 'Browser not initialized' };

        try {
            // Check if page is still valid
            if (page.isClosed()) {
                return { error: 'Page is closed' };
            }

            // Wait a bit for any ongoing navigation to complete
            await new Promise(resolve => setTimeout(resolve, 500));

            const url = page.url();
            const title = await page.title();

            // Get HTML content with timeout
            const html = await Promise.race([
                page.content(),
                new Promise((_, reject) => setTimeout(() => reject(new Error('Content timeout')), 5000))
            ]);

            // Get all interactive elements with error handling
            let elements = { inputs: [], buttons: [], links: [] };

            try {
                elements = await Promise.race([
                    page.evaluate(() => {
                        const inputs = Array.from(document.querySelectorAll('input')).map((el, index) => ({
                            type: 'input',
                            index,
                            inputType: el.type,
                            name: el.name,
                            id: el.id,
                            placeholder: el.placeholder,
                            value: el.value,
                            className: el.className,
                            selector: el.id ? `#${el.id}` : el.name ? `input[name="${el.name}"]` : `input:nth-of-type(${index + 1})`
                        }));

                        const buttons = Array.from(document.querySelectorAll('button, input[type="submit"], input[type="button"]')).map((el, index) => ({
                            type: 'button',
                            index,
                            text: el.textContent?.trim() || el.value,
                            className: el.className,
                            id: el.id,
                            selector: el.id ? `#${el.id}` : `button:nth-of-type(${index + 1})`
                        }));

                        const links = Array.from(document.querySelectorAll('a')).map((el, index) => ({
                            type: 'link',
                            index,
                            text: el.textContent?.trim(),
                            href: el.href,
                            className: el.className,
                            id: el.id,
                            selector: el.id ? `#${el.id}` : `a:nth-of-type(${index + 1})`
                        })).filter(link => link.text && link.text.length > 0);

                        return { inputs, buttons, links };
                    }),
                    new Promise((_, reject) => setTimeout(() => reject(new Error('Elements timeout')), 3000))
                ]);
            } catch (evalError) {
                console.log('Warning: Could not evaluate page elements:', evalError.message);
                // Continue with empty elements
            }

            return {
                success: true,
                url,
                title,
                html,
                elements,
                step: currentStep
            };

        } catch (error) {
            console.error('Page state error:', error.message);

            // If it's a detached frame error, try to reinitialize
            if (error.message.includes('detached Frame') || error.message.includes('Target closed')) {
                console.log('🔄 Reinitializing browser due to detached frame...');
                try {
                    await this.reinitialize();
                    return await this.getPageState(); // Retry once
                } catch (reinitError) {
                    return { success: false, error: 'Browser reinitialization failed: ' + reinitError.message };
                }
            }

            return { success: false, error: error.message };
        }
    }

    async reinitialize() {
        console.log('🔄 Reinitializing browser...');

        // Close existing browser if it exists
        if (browser && !browser.process()?.killed) {
            try {
                await browser.close();
            } catch (e) {
                console.log('Warning: Error closing browser:', e.message);
            }
        }

        // Reset state
        browser = null;
        page = null;
        this.initialized = false;

        // Reinitialize
        await this.initialize();
    }
}

const datDebugger = new DATDebugger();

// Initialize on startup
datDebugger.initialize();

// API Routes
app.get('/api/status', async (req, res) => {
    const state = await datDebugger.getPageState();
    res.json(state);
});

// Simple test endpoint
app.post('/api/test-google', async (req, res) => {
    try {
        console.log('🧪 Testing with Google...');
        currentStep = 'testing_google';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        await page.goto('https://www.google.com', {
            waitUntil: 'domcontentloaded',
            timeout: 15000
        });

        currentStep = 'google_loaded';
        console.log('✅ Google loaded successfully');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Google test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

// Test endpoint for simple navigation
app.post('/api/test', async (req, res) => {
    try {
        console.log('🧪 Testing with simple page...');
        currentStep = 'testing';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        await page.goto('https://httpbin.org/html', {
            waitUntil: 'domcontentloaded',
            timeout: 15000
        });

        currentStep = 'test_complete';
        console.log('✅ Test navigation complete');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/navigate', async (req, res) => {
    const { url } = req.body;

    try {
        console.log(`🌐 Navigating to: ${url || 'https://one.dat.com/dashboard'}`);
        currentStep = 'navigating';

        // Ensure we have a valid page
        if (!page || page.isClosed()) {
            console.log('🔄 Page not available, reinitializing...');
            await datDebugger.reinitialize();
        }

        // Add request/response logging
        page.on('response', response => {
            const status = response.status();
            const url = response.url();
            if (status >= 400) {
                console.log(`❌ Response error: ${status} ${url}`);
            } else {
                console.log(`📥 Response: ${status} ${url}`);
            }
        });

        page.on('requestfailed', request => {
            console.log(`❌ Request failed: ${request.url()} - ${request.failure()?.errorText || 'Unknown error'}`);
        });

        page.on('error', error => {
            console.log(`🚨 Page error: ${error.message}`);
        });

        const targetUrl = url || 'https://one.dat.com/dashboard';
        console.log(`🎯 Attempting to load: ${targetUrl}`);

        // Check session before navigation
        const cookiesBefore = await page.cookies();
        console.log(`🍪 Cookies before navigation: ${cookiesBefore.length}`);

        const response = await page.goto(targetUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        // Check session after navigation
        const cookiesAfter = await page.cookies();
        console.log(`🍪 Cookies after navigation: ${cookiesAfter.length}`);

        // Check if we're on a login page or dashboard
        const currentUrl = page.url();
        if (currentUrl.includes('login.dat.com')) {
            console.log('🔐 Redirected to login page - no valid session');
        } else if (currentUrl.includes('one.dat.com/dashboard')) {
            console.log('✅ On dashboard - session is valid');
        } else {
            console.log(`🤔 Unexpected URL: ${currentUrl}`);
        }

        console.log(`📊 Response status: ${response.status()}`);
        console.log(`📊 Response headers:`, response.headers());

        // Wait a bit for any redirects or dynamic content
        await new Promise(resolve => setTimeout(resolve, 2000));

        currentStep = 'page_loaded';
        const finalUrl = page.url();
        console.log(`✅ Navigation complete. Final URL: ${finalUrl}`);

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Navigation error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/type', async (req, res) => {
    const { selector, text } = req.body;

    try {
        console.log(`⌨️  Typing "${text}" in: ${selector}`);
        currentStep = 'typing';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        // Wait for element and ensure it's visible
        await page.waitForSelector(selector, { timeout: 5000, visible: true });

        // Focus the element
        await page.focus(selector);

        // Clear existing content
        await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            if (element) {
                element.value = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
            }
        }, selector);

        // Type the text with human-like delays
        await page.type(selector, text, { delay: 50 });

        // Trigger change event
        await page.evaluate((sel) => {
            const element = document.querySelector(sel);
            if (element) {
                element.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }, selector);

        currentStep = 'typed';
        console.log('✅ Text entered successfully');

        // Don't automatically refresh page state to avoid clearing the input
        res.json({
            success: true,
            message: 'Text entered successfully',
            selector,
            text,
            step: currentStep
        });

    } catch (error) {
        console.error('❌ Typing error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/click', async (req, res) => {
    const { selector } = req.body;

    try {
        console.log(`👆 Clicking: ${selector}`);
        currentStep = 'clicking';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        let actualSelector = selector;

        // Handle text-based selectors
        if (selector.includes('[text="')) {
            const textMatch = selector.match(/\[text="([^"]+)"\]/);
            if (textMatch) {
                const text = textMatch[1];
                const tagName = selector.split('[')[0];

                console.log(`🔍 Looking for ${tagName} with text: "${text}"`);

                // Find element by text content and click it directly
                const clickResult = await page.evaluate((tag, txt) => {
                    const elements = document.querySelectorAll(tag);
                    for (let el of elements) {
                        // Check both direct text and child element text (for Material Design buttons)
                        const elementText = el.textContent?.trim() || el.value || '';
                        const wrapperSpan = el.querySelector('.mat-button-wrapper');
                        const wrapperText = wrapperSpan ? wrapperSpan.textContent?.trim() || '' : '';

                        console.log(`Checking ${tag}: direct="${elementText}" wrapper="${wrapperText}"`);

                        if (elementText === txt || wrapperText === txt) {
                            console.log(`🎯 Found element with text "${txt}":`, el);

                            // Try multiple click methods
                            try {
                                // Method 1: Direct click
                                el.click();
                                console.log(`✅ Direct click successful on element with text "${txt}"`);
                                return { success: true, method: 'direct', selector: el.tagName.toLowerCase() };
                            } catch (e1) {
                                try {
                                    // Method 2: Dispatch click event
                                    const event = new MouseEvent('click', {
                                        view: window,
                                        bubbles: true,
                                        cancelable: true
                                    });
                                    el.dispatchEvent(event);
                                    console.log(`✅ Event dispatch successful on element with text "${txt}"`);
                                    return { success: true, method: 'event', selector: el.tagName.toLowerCase() };
                                } catch (e2) {
                                    try {
                                        // Method 3: Focus and trigger
                                        el.focus();
                                        el.click();
                                        console.log(`✅ Focus+click successful on element with text "${txt}"`);
                                        return { success: true, method: 'focus-click', selector: el.tagName.toLowerCase() };
                                    } catch (e3) {
                                        console.error(`❌ All click methods failed for "${txt}":`, e1, e2, e3);
                                        return { success: false, error: `All click methods failed: ${e1.message}` };
                                    }
                                }
                            }
                        }
                    }
                    return { success: false, error: `Element with text "${txt}" not found` };
                }, tagName, text);

                if (!clickResult.success) {
                    throw new Error(clickResult.error);
                }

                console.log(`🎯 Text-based click successful: ${clickResult.method} method`);
                actualSelector = clickResult.selector; // For logging purposes
            }
        }

        // Get current URL before click
        const urlBefore = page.url();
        console.log(`📍 URL before click: ${urlBefore}`);

        // Only do regular click if we haven't already handled text-based clicking
        if (!selector.includes('[text="')) {
            await page.waitForSelector(actualSelector, { timeout: 5000 });
            await page.click(actualSelector);
        } else {
            console.log('✅ Text-based click already executed, skipping regular click');
        }

        // Wait for navigation or changes
        console.log('⏳ Waiting for navigation or changes...');

        try {
            // Wait for either navigation or timeout
            await Promise.race([
                page.waitForNavigation({ timeout: 5000 }),
                new Promise(resolve => setTimeout(resolve, 3000))
            ]);
        } catch (error) {
            console.log('⚠️ No navigation detected, checking for other changes...');
        }

        // Get URL after click
        const urlAfter = page.url();
        console.log(`📍 URL after click: ${urlAfter}`);

        // Check if URL changed
        if (urlBefore !== urlAfter) {
            console.log(`🌐 Navigation detected: ${urlBefore} → ${urlAfter}`);
        } else {
            console.log('📍 No navigation occurred, checking for dynamic content changes...');

            // Check for any error messages or loading states
            const errorMessages = await page.evaluate(() => {
                const errors = [];

                // Check for common error selectors
                const errorSelectors = [
                    '.error', '.alert-danger', '.mat-error', '[role="alert"]',
                    '.notification-error', '.toast-error', '.snackbar-error'
                ];

                errorSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        if (el.textContent && el.textContent.trim()) {
                            errors.push({
                                selector: selector,
                                text: el.textContent.trim(),
                                visible: el.offsetParent !== null
                            });
                        }
                    });
                });

                return errors;
            });

            if (errorMessages.length > 0) {
                console.log('❌ Error messages found:', errorMessages);
            }

            // Check for loading indicators
            const loadingStates = await page.evaluate(() => {
                const loading = [];
                const loadingSelectors = [
                    '.loading', '.spinner', '.mat-spinner', '[role="progressbar"]',
                    '.progress', '.loader'
                ];

                loadingSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => {
                        if (el.offsetParent !== null) {
                            loading.push({
                                selector: selector,
                                visible: true
                            });
                        }
                    });
                });

                return loading;
            });

            if (loadingStates.length > 0) {
                console.log('⏳ Loading indicators found:', loadingStates);
                // Wait a bit more for loading to complete
                await new Promise(resolve => setTimeout(resolve, 3000));

                const finalUrl = page.url();
                console.log(`📍 Final URL after loading: ${finalUrl}`);
            }
        }

        currentStep = 'clicked';
        console.log('✅ Element clicked and analyzed');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Click error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

app.post('/api/screenshot', async (req, res) => {
    try {
        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        // Get current page info before screenshot
        const url = page.url();
        const title = await page.title();
        console.log(`📸 Taking screenshot of: ${url} (${title})`);

        const filename = `screenshot-${Date.now()}.png`;
        await page.screenshot({
            path: `debug-public/${filename}`,
            fullPage: true
        });

        console.log(`📸 Screenshot saved: ${filename}`);
        res.json({ success: true, filename, url, title });

    } catch (error) {
        console.error('❌ Screenshot error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Navigation endpoint to handle link clicks
app.post('/api/navigate', async (req, res) => {
    const { url } = req.body;

    try {
        console.log(`🌐 Navigation request: ${url}`);
        currentStep = 'navigating';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        const urlBefore = page.url();
        console.log(`📍 Current URL: ${urlBefore}`);
        console.log(`📍 Navigating to: ${url}`);

        // Navigate to the new URL
        const response = await page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        console.log(`📊 Navigation response status: ${response.status()}`);

        // Wait for page to fully load
        await new Promise(resolve => setTimeout(resolve, 2000));

        const urlAfter = page.url();
        console.log(`📍 Final URL after navigation: ${urlAfter}`);

        currentStep = 'navigated';
        console.log('✅ Navigation completed successfully');

        const state = await datDebugger.getPageState();
        res.json({ ...state, urlBefore, urlAfter, navigationSuccessful: true });

    } catch (error) {
        console.error('❌ Navigation error:', error);
        currentStep = 'navigation-error';
        res.json({ success: false, error: error.message });
    }
});

// Enhanced click endpoint with multiple methods
app.post('/api/click-enhanced', async (req, res) => {
    const { selector, method = 'auto' } = req.body;

    try {
        console.log(`🎯 Enhanced clicking: ${selector} (method: ${method})`);
        currentStep = 'enhanced-clicking';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        const urlBefore = page.url();
        console.log(`📍 URL before enhanced click: ${urlBefore}`);

        let actualSelector = selector;

        // Handle text-based selectors
        if (selector.includes('[text="')) {
            const textMatch = selector.match(/\[text="([^"]+)"\]/);
            if (textMatch) {
                const text = textMatch[1];
                const tagName = selector.split('[')[0];

                actualSelector = await page.evaluate((tag, txt) => {
                    const elements = document.querySelectorAll(tag);
                    for (let el of elements) {
                        const elementText = el.textContent?.trim() || el.value || '';
                        if (elementText === txt) {
                            if (el.id) return `#${el.id}`;
                            if (el.name) return `${tag}[name="${el.name}"]`;
                            if (el.className) {
                                const classes = el.className.split(' ').filter(c => c.length > 0);
                                if (classes.length > 0) return `${tag}.${classes[0]}`;
                            }
                            const siblings = Array.from(document.querySelectorAll(tag));
                            const index = siblings.indexOf(el) + 1;
                            return `${tag}:nth-of-type(${index})`;
                        }
                    }
                    return null;
                }, tagName, text);

                if (!actualSelector) {
                    throw new Error(`Could not find ${tagName} with text "${text}"`);
                }
                console.log(`🎯 Resolved selector: ${actualSelector}`);
            }
        }

        await page.waitForSelector(actualSelector, { timeout: 5000 });

        // Try different click methods based on the method parameter
        switch (method) {
            case 'javascript':
                console.log('🖱️ Using JavaScript click...');
                await page.evaluate((sel) => {
                    const element = document.querySelector(sel);
                    if (element) {
                        element.click();
                        return true;
                    }
                    return false;
                }, actualSelector);
                break;

            case 'force':
                console.log('🖱️ Using force click...');
                await page.click(actualSelector, { force: true });
                break;

            case 'hover-click':
                console.log('🖱️ Using hover then click...');
                await page.hover(actualSelector);
                await new Promise(resolve => setTimeout(resolve, 500));
                await page.click(actualSelector);
                break;

            case 'double-click':
                console.log('🖱️ Using double click...');
                await page.dblclick(actualSelector);
                break;

            default: // 'auto' or any other value
                console.log('🖱️ Using standard click...');
                await page.click(actualSelector);
                break;
        }

        // Wait for changes with enhanced monitoring
        console.log('⏳ Monitoring for changes...');

        let navigationOccurred = false;
        try {
            await page.waitForNavigation({ timeout: 3000 });
            navigationOccurred = true;
        } catch (error) {
            // No navigation, check for other changes
        }

        const urlAfter = page.url();
        console.log(`📍 URL after enhanced click: ${urlAfter}`);

        if (navigationOccurred || urlBefore !== urlAfter) {
            console.log(`🌐 Navigation successful: ${urlBefore} → ${urlAfter}`);
        } else {
            // Check for dynamic content changes
            await new Promise(resolve => setTimeout(resolve, 2000));

            const changes = await page.evaluate(() => {
                return {
                    hasLoadingIndicators: document.querySelectorAll('.loading, .spinner, .mat-spinner').length > 0,
                    hasErrorMessages: document.querySelectorAll('.error, .alert-danger, .mat-error').length > 0,
                    currentUrl: window.location.href,
                    pageTitle: document.title
                };
            });

            console.log('📊 Page state after click:', changes);
        }

        currentStep = 'enhanced-clicked';
        console.log('✅ Enhanced click completed');

        const state = await datDebugger.getPageState();
        res.json({ ...state, method: method, urlBefore, urlAfter, navigationOccurred });

    } catch (error) {
        console.error('❌ Enhanced click error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message, method });
    }
});

// Debug endpoint to get all buttons on current page
app.post('/api/debug-buttons', async (req, res) => {
    try {
        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        console.log('🔍 Analyzing all buttons on current page...');

        const buttons = await page.evaluate(() => {
            const allButtons = document.querySelectorAll('button, input[type="button"], input[type="submit"], a');

            function generateBestSelector(element) {
                // Priority 1: ID
                if (element.id) {
                    return `#${element.id}`;
                }

                // Priority 2: Name + Value combination (for buttons/inputs)
                if (element.name && element.value) {
                    return `${element.tagName.toLowerCase()}[name="${element.name}"][value="${element.value}"]`;
                }

                // Priority 3: Name attribute only
                if (element.name) {
                    return `${element.tagName.toLowerCase()}[name="${element.name}"]`;
                }

                // Priority 4: Value attribute (for inputs/buttons without name)
                if (element.value && element.value.length > 0) {
                    const sameValueElements = document.querySelectorAll(`${element.tagName.toLowerCase()}[value="${element.value}"]`);
                    if (sameValueElements.length === 1) {
                        return `${element.tagName.toLowerCase()}[value="${element.value}"]`;
                    }
                }

                // Priority 5: Text content (for buttons with unique text)
                const text = element.textContent?.trim() || element.value || '';
                if (text && text.length > 0 && text.length < 50) {
                    // Check if text is unique on the page
                    const sameTextElements = document.querySelectorAll(`${element.tagName.toLowerCase()}`);
                    const uniqueText = Array.from(sameTextElements).filter(el =>
                        (el.textContent?.trim() || el.value || '') === text
                    ).length === 1;

                    if (uniqueText) {
                        return `${element.tagName.toLowerCase()}[text="${text}"]`;
                    }
                }

                // Priority 6: Class-based selector (more specific)
                if (element.className) {
                    const classes = element.className.split(' ').filter(c => c.length > 0);
                    if (classes.length > 0) {
                        // Try with first class
                        const classSelector = `${element.tagName.toLowerCase()}.${classes[0]}`;
                        const sameClassElements = document.querySelectorAll(classSelector);
                        if (sameClassElements.length === 1) {
                            return classSelector;
                        }

                        // Try with multiple classes if needed
                        if (classes.length > 1) {
                            const multiClassSelector = `${element.tagName.toLowerCase()}.${classes.slice(0, 2).join('.')}`;
                            const multiClassElements = document.querySelectorAll(multiClassSelector);
                            if (multiClassElements.length === 1) {
                                return multiClassSelector;
                            }
                        }
                    }
                }

                // Priority 7: Parent-based selector
                const parent = element.parentElement;
                if (parent) {
                    const parentSelector = parent.id ? `#${parent.id}` :
                                         parent.className ? `.${parent.className.split(' ')[0]}` :
                                         parent.tagName.toLowerCase();

                    const childButtons = parent.querySelectorAll(element.tagName.toLowerCase());
                    const childIndex = Array.from(childButtons).indexOf(element) + 1;

                    if (childButtons.length > 1) {
                        return `${parentSelector} > ${element.tagName.toLowerCase()}:nth-child(${childIndex})`;
                    }
                }

                // Fallback: nth-of-type (least reliable)
                const siblings = Array.from(document.querySelectorAll(element.tagName.toLowerCase()));
                const index = siblings.indexOf(element) + 1;
                return `${element.tagName.toLowerCase()}:nth-of-type(${index})`;
            }

            return Array.from(allButtons).map((btn, index) => ({
                index: index + 1,
                tagName: btn.tagName,
                type: btn.type,
                text: btn.textContent?.trim() || btn.value || '',
                id: btn.id,
                name: btn.name,
                value: btn.value,
                className: btn.className,
                selector: generateBestSelector(btn),
                visible: btn.offsetParent !== null,
                disabled: btn.disabled,
                rect: btn.getBoundingClientRect()
            }));
        });

        console.log(`🔍 Found ${buttons.length} clickable elements:`);
        buttons.forEach(btn => {
            const valueInfo = btn.value ? ` value="${btn.value}"` : '';
            const modalInfo = btn.className && btn.className.includes('mat-') ? ' [MODAL]' : '';
            console.log(`  ${btn.index}. ${btn.tagName} "${btn.text}" - ${btn.selector}${valueInfo}${modalInfo} (visible: ${btn.visible}, disabled: ${btn.disabled})`);
        });

        // Check for modal dialogs specifically
        const modalButtons = buttons.filter(btn =>
            btn.text.toLowerCase().includes('login anyway') ||
            btn.text.toLowerCase().includes('go back') ||
            btn.className.includes('mat-dialog') ||
            btn.className.includes('mat-button')
        );

        if (modalButtons.length > 0) {
            console.log(`🚨 MODAL DETECTED! Found ${modalButtons.length} modal buttons:`);
            modalButtons.forEach(btn => {
                console.log(`  🔘 MODAL: "${btn.text}" - ${btn.selector}`);
            });
        }

        res.json({ success: true, buttons, count: buttons.length });

    } catch (error) {
        console.error('❌ Debug buttons error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Modal interaction endpoint
app.post('/api/click-modal', async (req, res) => {
    const { action = 'login-anyway' } = req.body;

    try {
        console.log(`🚨 Modal action requested: ${action}`);
        currentStep = 'modal-clicking';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        let selector;
        if (action === 'login-anyway') {
            // Try specific selectors for Material Design "LOGIN ANYWAY" button
            const selectors = [
                'button[mat-dialog-close][color="primary"]',
                'button[mat-dialog-close]',
                'button.mat-primary[mat-dialog-close]',
                'button.mat-primary',
                '.mat-dialog-actions button:last-child',
                'button[color="primary"]'
            ];

            console.log('🔍 Trying Material Design button selectors...');

            for (const sel of selectors) {
                try {
                    const element = await page.$(sel);
                    if (element) {
                        const text = await element.evaluate(el => el.textContent?.trim() || '');
                        console.log(`🔍 Found button with selector "${sel}": "${text}"`);

                        if (text.toLowerCase().includes('login')) {
                            selector = sel;
                            console.log(`✅ Found modal button: "${text}" with selector: ${selector}`);
                            break;
                        }
                    }
                } catch (e) {
                    console.log(`❌ Selector "${sel}" failed:`, e.message);
                }
            }

            // Enhanced fallback: find by text content in button or child elements
            if (!selector) {
                console.log('🔍 Trying enhanced text-based search...');

                const result = await page.evaluate(() => {
                    const buttons = document.querySelectorAll('button');
                    for (let btn of buttons) {
                        // Check button text and child element text
                        const buttonText = btn.textContent?.trim() || '';
                        const wrapperSpan = btn.querySelector('.mat-button-wrapper');
                        const wrapperText = wrapperSpan ? wrapperSpan.textContent?.trim() || '' : '';

                        console.log(`Checking button: "${buttonText}" | wrapper: "${wrapperText}"`);

                        if (buttonText.toLowerCase().includes('login anyway') ||
                            wrapperText.toLowerCase().includes('login anyway')) {

                            // Generate specific selector for this button
                            if (btn.hasAttribute('mat-dialog-close')) {
                                return { selector: 'button[mat-dialog-close]', text: buttonText || wrapperText };
                            }
                            if (btn.classList.contains('mat-primary')) {
                                return { selector: 'button.mat-primary', text: buttonText || wrapperText };
                            }
                            if (btn.id) {
                                return { selector: `#${btn.id}`, text: buttonText || wrapperText };
                            }

                            // Use class-based selector
                            const classes = Array.from(btn.classList).filter(c => c.length > 0);
                            if (classes.length > 0) {
                                return { selector: `button.${classes[0]}`, text: buttonText || wrapperText };
                            }

                            // Fallback to nth-of-type
                            const allButtons = Array.from(document.querySelectorAll('button'));
                            const index = allButtons.indexOf(btn) + 1;
                            return { selector: `button:nth-of-type(${index})`, text: buttonText || wrapperText };
                        }
                    }
                    return null;
                });

                if (result) {
                    selector = result.selector;
                    console.log(`✅ Found button by text search: "${result.text}" with selector: ${selector}`);
                } else {
                    console.log('❌ No LOGIN ANYWAY button found');
                }
            }
        }

        if (!selector) {
            throw new Error(`Could not find modal button for action: ${action}`);
        }

        console.log(`🎯 Clicking modal button: ${selector}`);

        // Wait for element and click
        await page.waitForSelector(selector, { timeout: 5000 });
        await page.click(selector);

        // Wait for modal to close and handle potential redirects
        console.log('⏳ Waiting for modal to close and checking for redirects...');
        await new Promise(resolve => setTimeout(resolve, 3000));

        // Check if we were redirected to login page
        const currentUrl = page.url();
        console.log(`📍 Current URL after modal click: ${currentUrl}`);

        if (currentUrl.includes('login.dat.com')) {
            console.log('🔐 Redirected to login page - need to re-authenticate');

            // Check if we're on the identifier page (email input)
            const hasEmailInput = await page.$('#username');
            if (hasEmailInput) {
                console.log('📧 On email input page - entering credentials...');

                // Enter email
                await page.type('#username', '<EMAIL>');
                await page.click('button[name="action"][value="default"]');

                // Wait for password page
                await new Promise(resolve => setTimeout(resolve, 2000));

                // Enter password
                const hasPasswordInput = await page.$('#password');
                if (hasPasswordInput) {
                    console.log('🔑 On password page - entering password...');
                    await page.type('#password', 'm6BKJVYzJDugC9!');
                    await page.click('button[name="action"][value="default"]');

                    // Wait for login to complete
                    await new Promise(resolve => setTimeout(resolve, 5000));

                    const finalUrl = page.url();
                    console.log(`📍 Final URL after re-authentication: ${finalUrl}`);

                    if (finalUrl.includes('one.dat.com/dashboard')) {
                        console.log('✅ Successfully re-authenticated and reached dashboard');
                    } else {
                        console.log('⚠️ Re-authentication may have failed or redirected elsewhere');
                    }
                }
            }
        } else if (currentUrl.includes('one.dat.com/dashboard')) {
            console.log('✅ Successfully stayed on dashboard - modal resolved');
        } else {
            console.log(`🤔 Unexpected URL after modal click: ${currentUrl}`);
        }

        currentStep = 'modal-clicked';
        console.log('✅ Modal button clicked and handled successfully');

        const state = await datDebugger.getPageState();
        res.json({ ...state, modalAction: action, selector });

    } catch (error) {
        console.error('❌ Modal click error:', error);
        currentStep = 'modal-error';
        res.json({ success: false, error: error.message, modalAction: action });
    }
});

// Complete login flow endpoint
app.post('/api/complete-login', async (req, res) => {
    try {
        console.log('🔐 Starting complete login flow...');
        currentStep = 'complete-login';

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Page not available' });
        }

        // Navigate to DAT dashboard
        console.log('🌐 Navigating to DAT dashboard...');
        await page.goto('https://one.dat.com/dashboard', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });

        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 3000));

        let currentUrl = page.url();
        console.log(`📍 Current URL: ${currentUrl}`);

        // Handle login flow if redirected
        if (currentUrl.includes('login.dat.com')) {
            console.log('🔐 On login page - starting authentication...');

            // Step 1: Email
            const emailInput = await page.$('#username');
            if (emailInput) {
                console.log('📧 Entering email...');
                await page.type('#username', '<EMAIL>');
                await page.click('button[name="action"][value="default"]');
                await new Promise(resolve => setTimeout(resolve, 2000));
            }

            // Step 2: Password
            const passwordInput = await page.$('#password');
            if (passwordInput) {
                console.log('🔑 Entering password...');
                await page.type('#password', 'm6BKJVYzJDugC9!');
                await page.click('button[name="action"][value="default"]');
                await new Promise(resolve => setTimeout(resolve, 5000));
            }

            // Step 3: Handle modal if it appears
            currentUrl = page.url();
            console.log(`📍 URL after login: ${currentUrl}`);

            // Check for modal
            const modal = await page.$('.mat-dialog-container');
            if (modal) {
                console.log('🚨 Modal detected - clicking LOGIN ANYWAY...');
                const loginAnywayBtn = await page.$('button[mat-dialog-close]');
                if (loginAnywayBtn) {
                    await loginAnywayBtn.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));

                    // Check final result
                    currentUrl = page.url();
                    console.log(`📍 Final URL after modal: ${currentUrl}`);
                }
            }
        }

        // Final status check
        if (currentUrl.includes('one.dat.com/dashboard')) {
            console.log('✅ Login completed successfully - on dashboard');
            currentStep = 'logged-in';
        } else {
            console.log(`⚠️ Login may not be complete - current URL: ${currentUrl}`);
            currentStep = 'login-uncertain';
        }

        const state = await datDebugger.getPageState();
        res.json({ ...state, loginCompleted: currentUrl.includes('one.dat.com/dashboard') });

    } catch (error) {
        console.error('❌ Complete login error:', error);
        currentStep = 'login-error';
        res.json({ success: false, error: error.message });
    }
});

// Test endpoint with simple page
app.post('/api/test-simple', async (req, res) => {
    try {
        console.log('🧪 Testing with simple page...');
        currentStep = 'testing';

        if (!page || page.isClosed()) {
            await datDebugger.reinitialize();
        }

        // Try a very simple page first
        await page.goto('data:text/html,<html><body><h1>Test Page</h1><input id="test" placeholder="Test input"><button>Test Button</button></body></html>', {
            waitUntil: 'domcontentloaded',
            timeout: 5000
        });

        currentStep = 'test_complete';
        console.log('✅ Simple test complete');

        const state = await datDebugger.getPageState();
        res.json(state);

    } catch (error) {
        console.error('❌ Test error:', error);
        currentStep = 'error';
        res.json({ success: false, error: error.message });
    }
});

// Cookie extraction endpoint for session transfer
app.get('/api/get-session-cookies', async (req, res) => {
    try {
        console.log('🍪 Extracting session cookies from backend browser...');

        if (!page || page.isClosed()) {
            return res.json({ success: false, error: 'Backend browser not available' });
        }

        // Get all cookies from the backend browser
        const cookies = await page.cookies();
        console.log(`🍪 Found ${cookies.length} cookies in backend browser`);

        // Filter for DAT-related cookies
        const datCookies = cookies.filter(cookie =>
            cookie.domain.includes('dat.com') ||
            cookie.domain.includes('.dat.com')
        );

        console.log(`🍪 Found ${datCookies.length} DAT cookies:`);
        datCookies.forEach(cookie => {
            console.log(`  🍪 ${cookie.name}: ${cookie.domain} (${cookie.secure ? 'secure' : 'insecure'})`);
        });

        res.json({
            success: true,
            cookies: datCookies,
            count: datCookies.length
        });

    } catch (error) {
        console.error('❌ Cookie extraction error:', error);
        res.json({ success: false, error: error.message });
    }
});

// Serve the debug interface
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'debug-public', 'index.html'));
});

const server = app.listen(PORT, '0.0.0.0', () => {
    console.log(`🎯 DAT Debug Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log('🚀 Ready to debug DAT login flow!');
});

server.on('error', (err) => {
    if (err.code === 'EADDRINUSE') {
        console.error(`❌ Port ${PORT} is already in use. Please kill the existing process or use a different port.`);
        process.exit(1);
    } else {
        console.error('❌ Server error:', err);
        process.exit(1);
    }
});

// Graceful shutdown
process.on('SIGINT', async () => {
    console.log('🧹 Shutting down...');
    if (browser) {
        await browser.close();
    }
    process.exit(0);
});
