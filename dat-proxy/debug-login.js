const puppeteer = require('puppeteer');

class DATLoginDebugger {
    constructor() {
        this.browser = null;
        this.page = null;
    }

    async initialize() {
        console.log('🚀 Initializing browser...');
        
        this.browser = await puppeteer.launch({
            headless: true, // Must be true on server without display
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor'
            ]
        });

        this.page = await this.browser.newPage();
        
        // Set user agent to appear as a regular browser
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        
        // Set viewport
        await this.page.setViewport({ width: 1920, height: 1080 });
        
        // Remove automation indicators
        await this.page.evaluateOnNewDocument(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // Remove chrome automation indicators
            if (window.chrome && window.chrome.runtime) {
                delete window.chrome.runtime.onConnect;
                delete window.chrome.runtime.onMessage;
            }
            
            // Mock plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Mock languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
        });
        
        console.log('✅ Browser initialized');
    }

    async navigateToDAT() {
        console.log('🌐 Navigating to DAT dashboard...');
        
        try {
            await this.page.goto('https://one.dat.com/dashboard', { 
                waitUntil: 'networkidle2',
                timeout: 30000 
            });
            
            const currentUrl = this.page.url();
            console.log(`📍 Current URL: ${currentUrl}`);
            
            return { success: true, url: currentUrl };
        } catch (error) {
            console.error('❌ Navigation error:', error.message);
            return { success: false, error: error.message };
        }
    }

    async analyzeCurrentPage() {
        console.log('🔍 Analyzing current page...');
        
        try {
            const currentUrl = this.page.url();
            const title = await this.page.title();
            
            console.log(`📍 URL: ${currentUrl}`);
            console.log(`📄 Title: ${title}`);
            
            // Find all input fields
            const inputs = await this.page.evaluate(() => {
                const inputElements = document.querySelectorAll('input');
                return Array.from(inputElements).map(input => ({
                    type: input.type,
                    name: input.name,
                    id: input.id,
                    placeholder: input.placeholder,
                    className: input.className,
                    required: input.required
                }));
            });
            
            // Find all buttons
            const buttons = await this.page.evaluate(() => {
                const buttonElements = document.querySelectorAll('button, input[type="submit"], input[type="button"]');
                return Array.from(buttonElements).map(button => ({
                    type: button.type,
                    textContent: button.textContent?.trim(),
                    className: button.className,
                    id: button.id,
                    name: button.name
                }));
            });
            
            // Find all links
            const links = await this.page.evaluate(() => {
                const linkElements = document.querySelectorAll('a');
                return Array.from(linkElements).map(link => ({
                    href: link.href,
                    textContent: link.textContent?.trim(),
                    className: link.className,
                    id: link.id
                })).filter(link => link.textContent && link.textContent.length > 0);
            });
            
            console.log('\n📝 INPUT FIELDS:');
            inputs.forEach((input, index) => {
                console.log(`  ${index + 1}. Type: ${input.type}, Name: ${input.name}, ID: ${input.id}, Placeholder: ${input.placeholder}`);
            });
            
            console.log('\n🔘 BUTTONS:');
            buttons.forEach((button, index) => {
                console.log(`  ${index + 1}. Text: "${button.textContent}", Type: ${button.type}, ID: ${button.id}`);
            });
            
            console.log('\n🔗 LINKS (first 10):');
            links.slice(0, 10).forEach((link, index) => {
                console.log(`  ${index + 1}. Text: "${link.textContent}", Href: ${link.href}`);
            });
            
            return {
                success: true,
                url: currentUrl,
                title: title,
                inputs: inputs,
                buttons: buttons,
                links: links
            };
            
        } catch (error) {
            console.error('❌ Analysis error:', error.message);
            return { success: false, error: error.message };
        }
    }

    async typeInField(fieldSelector, text) {
        console.log(`⌨️  Typing "${text}" in field: ${fieldSelector}`);
        
        try {
            await this.page.waitForSelector(fieldSelector, { timeout: 5000 });
            await this.page.click(fieldSelector);
            await this.page.keyboard.selectAll();
            await this.page.type(fieldSelector, text);
            
            console.log('✅ Text entered successfully');
            return { success: true };
        } catch (error) {
            console.error('❌ Typing error:', error.message);
            return { success: false, error: error.message };
        }
    }

    async clickElement(elementSelector) {
        console.log(`👆 Clicking element: ${elementSelector}`);
        
        try {
            await this.page.waitForSelector(elementSelector, { timeout: 5000 });
            await this.page.click(elementSelector);
            
            // Wait a bit for any navigation or changes
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            console.log('✅ Element clicked successfully');
            return { success: true };
        } catch (error) {
            console.error('❌ Click error:', error.message);
            return { success: false, error: error.message };
        }
    }

    async waitForNavigation() {
        console.log('⏳ Waiting for navigation...');
        
        try {
            await this.page.waitForNavigation({ 
                waitUntil: 'networkidle2', 
                timeout: 10000 
            });
            
            const newUrl = this.page.url();
            console.log(`✅ Navigation complete. New URL: ${newUrl}`);
            return { success: true, url: newUrl };
        } catch (error) {
            console.log('⚠️  No navigation detected or timeout');
            return { success: false, error: error.message };
        }
    }

    async takeScreenshot(filename = 'debug-screenshot.png') {
        console.log(`📸 Taking screenshot: ${filename}`);
        
        try {
            await this.page.screenshot({ 
                path: filename, 
                fullPage: true 
            });
            
            console.log('✅ Screenshot saved');
            return { success: true, filename: filename };
        } catch (error) {
            console.error('❌ Screenshot error:', error.message);
            return { success: false, error: error.message };
        }
    }

    async cleanup() {
        console.log('🧹 Cleaning up...');
        
        if (this.browser) {
            await this.browser.close();
            console.log('✅ Browser closed');
        }
    }
}

// Interactive debugging session
async function startDebugging() {
    const datDebugger = new DATLoginDebugger();

    try {
        await datDebugger.initialize();

        console.log('\n🎯 DAT Login Debugger Started!');
        console.log('📋 Available commands:');
        console.log('  1. navigate() - Go to DAT dashboard');
        console.log('  2. analyze() - Analyze current page elements');
        console.log('  3. type(selector, text) - Type text in a field');
        console.log('  4. click(selector) - Click an element');
        console.log('  5. wait() - Wait for navigation');
        console.log('  6. screenshot() - Take a screenshot');
        console.log('  7. cleanup() - Close browser and exit');

        // Make debugger available globally for interactive use
        global.datDebugger = datDebugger;
        global.navigate = () => datDebugger.navigateToDAT();
        global.analyze = () => datDebugger.analyzeCurrentPage();
        global.type = (selector, text) => datDebugger.typeInField(selector, text);
        global.click = (selector) => datDebugger.clickElement(selector);
        global.wait = () => datDebugger.waitForNavigation();
        global.screenshot = (filename) => datDebugger.takeScreenshot(filename);
        global.cleanup = () => datDebugger.cleanup();

        console.log('\n🚀 Ready! Start with: navigate()');

    } catch (error) {
        console.error('❌ Initialization failed:', error);
        await datDebugger.cleanup();
    }
}

// Start the debugging session
startDebugging();
