{"net": {"http_server_properties": {"servers": [{"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398271391416499", "port": 443, "protocol_str": "quic"}], "anonymization": ["MAAAACwAAABodHRwczovL3Bhc3N3b3Jkc2xlYWtjaGVjay1wYS5nb29nbGVhcGlzLmNvbQ==", false, 0], "server": "https://passwordsleakcheck-pa.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://hlp.prod.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://hlp.test.nprod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://hlp.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://cdn.auth0.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", true, 0], "server": "https://media.stonly.com", "supports_spdy": true}, {"anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "server": "https://redirector.gvt1.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAABAAAABodHRwczovL2d2dDEuY29t", false, 0], "network_stats": {"srtt": 2435}, "server": "https://r4---sn-ab5l6nrs.gvt1.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "*****************", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 7842}, "server": "https://accounts.google.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://static.hotjar.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.dashboard.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://load.sumo.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", true, 0], "server": "https://static.stonly.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", true, 0], "server": "https://shared-content.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", true, 0], "server": "https://stonly.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://script.hotjar.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://shared-content-cargo.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://js.api.here.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.prod-my-shipments.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.company-search-prod.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.prod-private-network.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://cdn.optimizely.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://cdn.segment.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://login.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.freight-search-prod.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://freight.api.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://one.dat.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398271480938117", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "network_stats": {"srtt": 2241}, "server": "https://content-autofill.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://load.sumome.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://sumome.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://stonly.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", true, 0], "server": "https://s.stonly.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://api.stonly.com", "supports_spdy": true}, {"anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 2284}, "server": "https://www.google.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398271427423204", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "network_stats": {"srtt": 1218}, "server": "https://fonts.gstatic.com"}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398272878140659", "port": 443, "protocol_str": "quic"}], "anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "network_stats": {"srtt": 8187}, "server": "https://maps.googleapis.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://api.segment.io", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://freight.api.prod.dat.com", "supports_spdy": true}, {"anonymization": ["FAAAAA8AAABodHRwczovL2RhdC5jb20A", false, 0], "server": "https://identity.api.dat.com", "supports_spdy": true}, {"alternative_service": [{"advertised_alpns": ["h3"], "expiration": "13398273178864078", "port": 443, "protocol_str": "quic"}], "anonymization": ["GAAAABIAAABodHRwczovL2dvb2dsZS5jb20AAA==", false, 0], "network_stats": {"srtt": 2535}, "server": "https://android.clients.google.com", "supports_spdy": true}], "supports_quic": {"address": "*************", "used_quic": true}, "version": 5}, "network_qualities": {"CAESABiAgICA+P////8B": "4G"}}}