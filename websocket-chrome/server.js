const express = require('express');
const WebSocket = require('ws');
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

const PORT = 3003;
const sessions = new Map();

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

class ChromeSession {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.browser = null;
        this.page = null;
        this.ws = null;
        this.lastActivity = Date.now();
        this.isInitialized = false;
        this.currentUrl = 'about:blank';
        this.domUpdateQueue = [];
        this.isProcessingQueue = false;
    }

    async initialize() {
        try {
            console.log(`🚀 Initializing Chrome session: ${this.sessionId}`);
            
            this.browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ],
                defaultViewport: { width: 1920, height: 1080 }
            });

            this.page = await this.browser.newPage();
            
            // Set up real-time DOM monitoring
            await this.setupDOMMonitoring();
            
            // Navigate to a blank page initially
            await this.page.goto('data:text/html,<html><body><h1>WebSocket Chrome Session Ready</h1><p>Session ID: ' + this.sessionId + '</p></body></html>');
            
            this.isInitialized = true;
            console.log(`✅ Chrome session initialized: ${this.sessionId}`);
            
            return true;
        } catch (error) {
            console.error(`❌ Failed to initialize Chrome session ${this.sessionId}:`, error);
            return false;
        }
    }

    async setupDOMMonitoring() {
        // Inject DOM monitoring script into every page
        await this.page.evaluateOnNewDocument(() => {
            // Real-time DOM change detection
            let changeQueue = [];
            let isProcessing = false;
            
            function sendDOMUpdate(type, data) {
                window.postMessage({
                    type: 'DOM_UPDATE',
                    updateType: type,
                    data: data,
                    timestamp: Date.now()
                }, '*');
            }
            
            function processQueue() {
                if (isProcessing || changeQueue.length === 0) return;
                
                isProcessing = true;
                
                // Send full DOM snapshot
                sendDOMUpdate('full', {
                    html: document.documentElement.outerHTML,
                    url: window.location.href,
                    title: document.title
                });
                
                changeQueue = [];
                isProcessing = false;
            }
            
            // Monitor DOM changes
            const observer = new MutationObserver((mutations) => {
                mutations.forEach(mutation => {
                    changeQueue.push({
                        type: mutation.type,
                        target: mutation.target.tagName,
                        addedNodes: mutation.addedNodes.length,
                        removedNodes: mutation.removedNodes.length
                    });
                });
                
                // Throttle updates to 60 FPS max
                setTimeout(processQueue, 16);
            });
            
            // Start observing when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => {
                    observer.observe(document.body || document.documentElement, {
                        childList: true,
                        subtree: true,
                        attributes: true,
                        characterData: true
                    });
                    processQueue(); // Send initial state
                });
            } else {
                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true,
                    attributes: true,
                    characterData: true
                });
                processQueue(); // Send initial state
            }
            
            // Monitor input events for immediate updates
            ['input', 'change', 'click', 'keydown', 'keyup'].forEach(eventType => {
                document.addEventListener(eventType, () => {
                    setTimeout(processQueue, 10); // Very fast response for input
                }, true);
            });
            
            // Monitor navigation
            let lastUrl = window.location.href;
            setInterval(() => {
                if (window.location.href !== lastUrl) {
                    lastUrl = window.location.href;
                    sendDOMUpdate('navigation', {
                        url: window.location.href,
                        title: document.title
                    });
                    setTimeout(processQueue, 100); // Update after navigation
                }
            }, 100);
        });
        
        // Listen for DOM updates from the page
        this.page.on('console', async (msg) => {
            if (msg.text().includes('DOM_UPDATE')) {
                // This is handled by the message listener below
            }
        });
        
        // Listen for postMessage events from injected script
        this.page.on('pageerror', error => {
            console.error(`Page error in ${this.sessionId}:`, error);
        });
        
        // Set up message listener for DOM updates
        await this.page.exposeFunction('sendDOMUpdate', (updateData) => {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                this.ws.send(JSON.stringify({
                    type: 'dom-update',
                    sessionId: this.sessionId,
                    ...updateData
                }));
            }
        });
        
        // Set up smart screenshot + DOM hybrid approach
        this.screenshotInterval = setInterval(async () => {
            if (this.page && this.ws && this.ws.readyState === WebSocket.OPEN) {
                try {
                    // Take screenshot for visual accuracy
                    const screenshot = await this.page.screenshot({
                        type: 'jpeg',
                        quality: 90,
                        fullPage: false
                    });

                    // Get DOM for interaction mapping
                    const content = await this.page.content();
                    const url = await this.page.url();
                    const title = await this.page.title();

                    // Send hybrid update
                    this.sendMessage({
                        type: 'hybrid-update',
                        data: {
                            screenshot: screenshot.toString('base64'),
                            html: content,
                            url: url,
                            title: title,
                            timestamp: Date.now()
                        }
                    });
                } catch (error) {
                    // Ignore errors during periodic updates
                }
            }
        }, 100); // 10 FPS for smooth experience
    }

    setWebSocket(ws) {
        this.ws = ws;
        console.log(`🔌 WebSocket connected to session: ${this.sessionId}`);
        
        // Send initial session info
        this.sendMessage({
            type: 'session-ready',
            sessionId: this.sessionId,
            currentUrl: this.currentUrl
        });
    }

    sendMessage(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        }
    }

    async navigate(url) {
        if (!this.page) return;
        
        try {
            console.log(`🌐 Navigating session ${this.sessionId} to: ${url}`);
            this.currentUrl = url;
            await this.page.goto(url, { waitUntil: 'domcontentloaded' });
            
            this.sendMessage({
                type: 'navigation-complete',
                url: url
            });
            
        } catch (error) {
            console.error(`❌ Navigation error for ${this.sessionId}:`, error);
            this.sendMessage({
                type: 'navigation-error',
                error: error.message
            });
        }
    }

    async handleMouseEvent(event) {
        if (!this.page) return;

        try {
            const { type, x, y, button } = event;

            switch (type) {
                case 'move':
                    await this.page.mouse.move(x, y);
                    break;
                case 'click':
                    await this.page.mouse.click(x, y, { button: button || 'left' });
                    break;
                case 'down':
                    await this.page.mouse.down({ button: button || 'left' });
                    break;
                case 'up':
                    await this.page.mouse.up({ button: button || 'left' });
                    break;
            }

            this.lastActivity = Date.now();

        } catch (error) {
            console.error(`❌ Mouse event error for ${this.sessionId}:`, error);
        }
    }

    async handleKeyboardEvent(event) {
        if (!this.page) return;

        try {
            const { type, key, text } = event;

            switch (type) {
                case 'keydown':
                    await this.page.keyboard.down(key);
                    break;
                case 'keyup':
                    await this.page.keyboard.up(key);
                    break;
                case 'type':
                    await this.page.keyboard.type(text);
                    break;
                case 'paste':
                    if (text) {
                        await this.page.keyboard.type(text);
                        console.log(`📋 Pasted text (${text.length} chars) for session: ${this.sessionId}`);
                    }
                    break;
            }

            this.lastActivity = Date.now();

        } catch (error) {
            console.error(`❌ Keyboard event error for ${this.sessionId}:`, error);
        }
    }

    async cleanup() {
        console.log(`🧹 Cleaning up session: ${this.sessionId}`);
        
        try {
            if (this.page) {
                await this.page.close();
            }
            if (this.browser) {
                await this.browser.close();
            }
        } catch (error) {
            console.error(`❌ Cleanup error for ${this.sessionId}:`, error);
        }

        sessions.delete(this.sessionId);
    }
}

// WebSocket connection handler
wss.on('connection', (ws) => {
    console.log('🔌 New WebSocket connection');
    
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            
            switch (data.type) {
                case 'create-session':
                    const sessionId = uuidv4();
                    const session = new ChromeSession(sessionId);
                    
                    if (await session.initialize()) {
                        sessions.set(sessionId, session);
                        session.setWebSocket(ws);
                        
                        ws.send(JSON.stringify({
                            type: 'session-created',
                            sessionId: sessionId
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Failed to initialize Chrome session'
                        }));
                    }
                    break;
                    
                case 'join-session':
                    const existingSession = sessions.get(data.sessionId);
                    if (existingSession) {
                        existingSession.setWebSocket(ws);
                    } else {
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Session not found'
                        }));
                    }
                    break;
                    
                case 'navigate':
                    const navSession = sessions.get(data.sessionId);
                    if (navSession) {
                        await navSession.navigate(data.url);
                    }
                    break;
                    
                case 'mouse-event':
                    const mouseSession = sessions.get(data.sessionId);
                    if (mouseSession) {
                        await mouseSession.handleMouseEvent(data);
                    }
                    break;
                    
                case 'keyboard-event':
                    const keySession = sessions.get(data.sessionId);
                    if (keySession) {
                        await keySession.handleKeyboardEvent(data);
                    }
                    break;
            }
            
        } catch (error) {
            console.error('❌ WebSocket message error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                error: error.message
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
    });
    
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
});

// Cleanup inactive sessions
setInterval(() => {
    const now = Date.now();
    for (const [sessionId, session] of sessions) {
        if (now - session.lastActivity > 300000) { // 5 minutes
            console.log(`🧹 Cleaning up inactive session: ${sessionId}`);
            session.cleanup();
        }
    }
}, 60000); // Check every minute

// Start server
server.listen(PORT, () => {
    console.log(`🚀 WebSocket Chrome Streaming Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`🎯 Ultra-responsive DOM streaming ready!`);
});
