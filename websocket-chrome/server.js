const express = require('express');
const WebSocket = require('ws');
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const path = require('path');
const http = require('http');

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });

const PORT = 3003;
const sessions = new Map();

// Serve static files
app.use(express.static(path.join(__dirname, 'public')));
app.use(express.json());

class ChromeSession {
    constructor(sessionId) {
        this.sessionId = sessionId;
        this.browser = null;
        this.page = null;
        this.ws = null;
        this.lastActivity = Date.now();
        this.isInitialized = false;
        this.currentUrl = 'about:blank';
        this.domUpdateQueue = [];
        this.isProcessingQueue = false;
    }

    async initialize() {
        try {
            console.log(`🚀 Initializing Chrome session: ${this.sessionId}`);
            
            this.browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ],
                defaultViewport: { width: 1920, height: 1080 }
            });

            this.page = await this.browser.newPage();
            
            // Set up real-time DOM monitoring
            await this.setupDOMMonitoring();
            
            // Navigate to a blank page initially
            await this.page.goto('data:text/html,<html><body><h1>WebSocket Chrome Session Ready</h1><p>Session ID: ' + this.sessionId + '</p></body></html>');
            
            this.isInitialized = true;
            console.log(`✅ Chrome session initialized: ${this.sessionId}`);
            
            return true;
        } catch (error) {
            console.error(`❌ Failed to initialize Chrome session ${this.sessionId}:`, error);
            return false;
        }
    }

    async setupDOMMonitoring() {
        // Simplified approach - just use screenshots with smart updates
        console.log(`📹 Setting up screenshot monitoring for session: ${this.sessionId}`);

        // Use the new screenshot monitoring method
        this.setupScreenshotMonitoring();

        // Take initial screenshot after a delay
        setTimeout(async () => {
            try {
                const screenshot = await this.page.screenshot({
                    type: 'jpeg',
                    quality: 85,
                    fullPage: false
                });

                let url = this.currentUrl;
                let title = 'Loading...';

                try {
                    url = await this.page.url();
                    title = await this.page.title();
                } catch (e) {
                    // Ignore errors getting page info
                }

                this.sendMessage({
                    type: 'hybrid-update',
                    data: {
                        screenshot: screenshot.toString('base64'),
                        url: url,
                        title: title,
                        timestamp: Date.now()
                    }
                });
            } catch (error) {
                console.error(`❌ Initial screenshot error for ${this.sessionId}:`, error);
            }
        }, 2000);
    }

    setWebSocket(ws) {
        this.ws = ws;
        console.log(`🔌 WebSocket connected to session: ${this.sessionId}`);
        
        // Send initial session info
        this.sendMessage({
            type: 'session-ready',
            sessionId: this.sessionId,
            currentUrl: this.currentUrl
        });
    }

    sendMessage(data) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(data));
        }
    }

    async navigate(url) {
        if (!this.page) return;

        try {
            console.log(`🌐 Navigating session ${this.sessionId} to: ${url}`);
            this.currentUrl = url;

            // Clear existing interval during navigation
            if (this.screenshotInterval) {
                clearInterval(this.screenshotInterval);
            }

            await this.page.goto(url, {
                waitUntil: 'networkidle0',
                timeout: 30000
            });

            // Restart screenshot monitoring after navigation
            this.setupScreenshotMonitoring();

            this.sendMessage({
                type: 'navigation-complete',
                url: url
            });

        } catch (error) {
            console.error(`❌ Navigation error for ${this.sessionId}:`, error);
            this.sendMessage({
                type: 'navigation-error',
                error: error.message
            });

            // Restart screenshot monitoring even if navigation failed
            this.setupScreenshotMonitoring();
        }
    }

    setupScreenshotMonitoring() {
        // Clear any existing interval
        if (this.screenshotInterval) {
            clearInterval(this.screenshotInterval);
        }

        // Set up screenshot monitoring
        this.screenshotInterval = setInterval(async () => {
            if (this.page && this.ws && this.ws.readyState === WebSocket.OPEN) {
                try {
                    // Take screenshot for visual accuracy
                    const screenshot = await this.page.screenshot({
                        type: 'jpeg',
                        quality: 85,
                        fullPage: false
                    });

                    // Get basic page info safely
                    let url = this.currentUrl;
                    let title = 'Loading...';

                    try {
                        url = await this.page.url();
                        title = await this.page.title();
                    } catch (e) {
                        // Ignore errors getting page info during navigation
                    }

                    // Send hybrid update
                    this.sendMessage({
                        type: 'hybrid-update',
                        data: {
                            screenshot: screenshot.toString('base64'),
                            url: url,
                            title: title,
                            timestamp: Date.now()
                        }
                    });
                } catch (error) {
                    // Silently ignore screenshot errors during navigation
                }
            }
        }, 100); // 10 FPS for smooth experience
    }

    async handleMouseEvent(event) {
        if (!this.page) return;

        try {
            const { type, x, y, button } = event;

            switch (type) {
                case 'move':
                    await this.page.mouse.move(x, y);
                    break;
                case 'click':
                    await this.page.mouse.click(x, y, { button: button || 'left' });
                    break;
                case 'down':
                    await this.page.mouse.down({ button: button || 'left' });
                    break;
                case 'up':
                    await this.page.mouse.up({ button: button || 'left' });
                    break;
            }

            this.lastActivity = Date.now();

        } catch (error) {
            console.error(`❌ Mouse event error for ${this.sessionId}:`, error);
        }
    }

    async handleKeyboardEvent(event) {
        if (!this.page) return;

        try {
            const { type, key, text } = event;

            switch (type) {
                case 'keydown':
                    await this.page.keyboard.down(key);
                    break;
                case 'keyup':
                    await this.page.keyboard.up(key);
                    break;
                case 'type':
                    await this.page.keyboard.type(text);
                    break;
                case 'paste':
                    if (text) {
                        await this.page.keyboard.type(text);
                        console.log(`📋 Pasted text (${text.length} chars) for session: ${this.sessionId}`);
                    }
                    break;
            }

            this.lastActivity = Date.now();

        } catch (error) {
            console.error(`❌ Keyboard event error for ${this.sessionId}:`, error);
        }
    }

    async cleanup() {
        console.log(`🧹 Cleaning up session: ${this.sessionId}`);

        try {
            // Clear screenshot interval
            if (this.screenshotInterval) {
                clearInterval(this.screenshotInterval);
            }

            if (this.page) {
                await this.page.close();
            }
            if (this.browser) {
                await this.browser.close();
            }
        } catch (error) {
            console.error(`❌ Cleanup error for ${this.sessionId}:`, error);
        }

        sessions.delete(this.sessionId);
    }
}

// WebSocket connection handler
wss.on('connection', (ws) => {
    console.log('🔌 New WebSocket connection');
    
    ws.on('message', async (message) => {
        try {
            const data = JSON.parse(message);
            
            switch (data.type) {
                case 'create-session':
                    const sessionId = uuidv4();
                    const session = new ChromeSession(sessionId);
                    
                    if (await session.initialize()) {
                        sessions.set(sessionId, session);
                        session.setWebSocket(ws);
                        
                        ws.send(JSON.stringify({
                            type: 'session-created',
                            sessionId: sessionId
                        }));
                    } else {
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Failed to initialize Chrome session'
                        }));
                    }
                    break;
                    
                case 'join-session':
                    const existingSession = sessions.get(data.sessionId);
                    if (existingSession) {
                        existingSession.setWebSocket(ws);
                    } else {
                        ws.send(JSON.stringify({
                            type: 'session-error',
                            error: 'Session not found'
                        }));
                    }
                    break;
                    
                case 'navigate':
                    const navSession = sessions.get(data.sessionId);
                    if (navSession) {
                        await navSession.navigate(data.url);
                    }
                    break;
                    
                case 'mouse-event':
                    const mouseSession = sessions.get(data.sessionId);
                    if (mouseSession) {
                        await mouseSession.handleMouseEvent(data);
                    }
                    break;
                    
                case 'keyboard-event':
                    const keySession = sessions.get(data.sessionId);
                    if (keySession) {
                        await keySession.handleKeyboardEvent(data);
                    }
                    break;
            }
            
        } catch (error) {
            console.error('❌ WebSocket message error:', error);
            ws.send(JSON.stringify({
                type: 'error',
                error: error.message
            }));
        }
    });
    
    ws.on('close', () => {
        console.log('🔌 WebSocket connection closed');
    });
    
    ws.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
    });
});

// Cleanup inactive sessions
setInterval(() => {
    const now = Date.now();
    for (const [sessionId, session] of sessions) {
        if (now - session.lastActivity > 300000) { // 5 minutes
            console.log(`🧹 Cleaning up inactive session: ${sessionId}`);
            session.cleanup();
        }
    }
}, 60000); // Check every minute

// Start server
server.listen(PORT, () => {
    console.log(`🚀 WebSocket Chrome Streaming Server running on port ${PORT}`);
    console.log(`🌐 Local: http://localhost:${PORT}`);
    console.log(`🌐 Network: http://*************:${PORT}`);
    console.log(`🎯 Ultra-responsive DOM streaming ready!`);
});
