<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Chrome Streaming - Ultra Responsive DAT Access</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        .header {
            background: #2d2d2d;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
            z-index: 1000;
            position: relative;
        }

        .logo {
            font-size: 18px;
            font-weight: bold;
            color: #00ff88;
        }

        .controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #0052a3;
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .btn.success {
            background: #00aa44;
        }

        .btn.success:hover {
            background: #008833;
        }

        .btn.danger {
            background: #cc0000;
        }

        .btn.danger:hover {
            background: #aa0000;
        }

        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }

        .status.connecting {
            background: #ff9900;
            color: #000;
        }

        .status.connected {
            background: #00aa44;
            color: #fff;
        }

        .status.error {
            background: #cc0000;
            color: #fff;
        }

        .main-container {
            height: calc(100vh - 60px);
            display: flex;
        }

        .sidebar {
            width: 300px;
            background: #2d2d2d;
            border-right: 1px solid #444;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #00ff88;
        }

        .session-info {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .session-info h4 {
            margin-bottom: 10px;
            color: #ffffff;
        }

        .session-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #ccc;
        }

        .quick-actions {
            margin-top: 20px;
        }

        .quick-actions .btn {
            width: 100%;
            margin-bottom: 10px;
        }

        .quick-actions input {
            width: 100%;
            margin-bottom: 10px;
            padding: 8px;
            border: 1px solid #666;
            background: #2d2d2d;
            color: #fff;
            border-radius: 4px;
        }

        .stream-container {
            flex: 1;
            background: #fff;
            position: relative;
            overflow: hidden;
        }

        .stream-frame {
            width: 100%;
            height: 100%;
            border: none;
            background: #fff;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #333;
            z-index: 10;
        }

        .loading .spinner {
            width: 50px;
            height: 50px;
            border: 3px solid #ddd;
            border-top: 3px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .overlay {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #00ff88;
            z-index: 100;
        }

        .performance-stats {
            background: #3d3d3d;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .performance-stats h4 {
            margin-bottom: 10px;
            color: #ffffff;
        }

        .stat {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
            font-size: 14px;
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            color: #00ff88;
            font-weight: bold;
        }

        .connection-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }

        .connection-indicator.connected {
            background: #00ff88;
            box-shadow: 0 0 10px #00ff88;
        }

        .connection-indicator.connecting {
            background: #ff9900;
            animation: pulse 1s infinite;
        }

        .connection-indicator.disconnected {
            background: #cc0000;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes clickFeedback {
            0% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(2);
                opacity: 0;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">⚡ WebSocket Chrome Streaming</div>
        <div class="controls">
            <span class="connection-indicator" id="connectionIndicator"></span>
            <span id="status" class="status connecting">Connecting...</span>
            <button id="createSession" class="btn">Create Session</button>
            <button id="disconnect" class="btn danger" disabled>Disconnect</button>
        </div>
    </div>

    <div class="main-container">
        <div class="sidebar">
            <h3>Session Control</h3>
            
            <div class="session-info">
                <h4>Current Session</h4>
                <p><strong>ID:</strong> <span id="sessionId">None</span></p>
                <p><strong>Status:</strong> <span id="sessionStatus">Disconnected</span></p>
                <p><strong>URL:</strong> <span id="currentUrl">-</span></p>
            </div>

            <div class="quick-actions">
                <h4>Quick Navigation</h4>
                <button class="btn" onclick="navigateTo('https://one.dat.com/dashboard')">📊 Dashboard</button>
                <button class="btn" onclick="navigateTo('https://one.dat.com/search-loads-ow')">🚛 Search Loads</button>
                <button class="btn" onclick="navigateTo('https://one.dat.com/my-shipments')">📦 My Shipments</button>
                <button class="btn" onclick="navigateTo('https://login.dat.com')">🔐 Login</button>
            </div>

            <div class="quick-actions">
                <h4>Quick Actions</h4>
                <input type="text" id="pasteText" placeholder="Text to paste (e.g., password)">
                <button class="btn" onclick="pasteText()">📋 Paste Text</button>
                <button class="btn" onclick="pastePassword()">🔑 Paste Password</button>
                <button class="btn" onclick="sendCtrlV()">⌨️ Ctrl+V</button>
            </div>

            <div class="performance-stats">
                <h4>Performance</h4>
                <div class="stat">
                    <span class="stat-label">Updates/sec:</span>
                    <span class="stat-value" id="updatesPerSec">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Latency:</span>
                    <span class="stat-value" id="latency">0ms</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Connection:</span>
                    <span class="stat-value" id="connectionStatus">WebSocket</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Data Mode:</span>
                    <span class="stat-value">DOM Streaming</span>
                </div>
            </div>
        </div>

        <div class="stream-container">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <h3>Initializing WebSocket Chrome Stream...</h3>
                <p>Setting up ultra-responsive DOM streaming</p>
            </div>
            
            <iframe id="streamFrame" class="stream-frame" style="display: none;"></iframe>
            
            <div class="overlay" id="overlay" style="display: none;">
                <div>⚡ LIVE DOM STREAM</div>
                <div>Ultra-responsive WebSocket</div>
            </div>
        </div>
    </div>

    <script src="client.js"></script>
</body>
</html>
