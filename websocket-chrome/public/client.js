class WebSocketChromeClient {
    constructor() {
        this.ws = null;
        this.sessionId = null;
        this.isConnected = false;
        this.stats = {
            updatesPerSec: 0,
            latency: 0,
            updateCount: 0,
            lastStatsUpdate: Date.now()
        };
        
        this.initializeUI();
        this.connect();
    }

    initializeUI() {
        // Get UI elements
        this.statusEl = document.getElementById('status');
        this.connectionIndicator = document.getElementById('connectionIndicator');
        this.createSessionBtn = document.getElementById('createSession');
        this.disconnectBtn = document.getElementById('disconnect');
        this.sessionIdEl = document.getElementById('sessionId');
        this.sessionStatusEl = document.getElementById('sessionStatus');
        this.currentUrlEl = document.getElementById('currentUrl');
        this.loadingEl = document.getElementById('loading');
        this.streamFrame = document.getElementById('streamFrame');
        this.overlay = document.getElementById('overlay');
        
        // Performance stats elements
        this.updatesPerSecEl = document.getElementById('updatesPerSec');
        this.latencyEl = document.getElementById('latency');
        this.connectionStatusEl = document.getElementById('connectionStatus');

        // Event listeners
        this.createSessionBtn.addEventListener('click', () => this.createSession());
        this.disconnectBtn.addEventListener('click', () => this.disconnect());
        
        // Set up iframe event forwarding
        this.setupIframeEventForwarding();
        
        // Start performance monitoring
        this.startPerformanceMonitoring();
    }

    connect() {
        console.log('🔌 Connecting to WebSocket server...');
        this.updateStatus('connecting', 'Connecting...');
        this.updateConnectionIndicator('connecting');
        
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('✅ Connected to WebSocket server');
            this.isConnected = true;
            this.updateStatus('connected', 'Connected');
            this.updateConnectionIndicator('connected');
            this.createSessionBtn.disabled = false;
        };

        this.ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                this.handleMessage(data);
            } catch (error) {
                console.error('❌ Failed to parse WebSocket message:', error);
            }
        };

        this.ws.onclose = () => {
            console.log('❌ WebSocket connection closed');
            this.isConnected = false;
            this.updateStatus('error', 'Disconnected');
            this.updateConnectionIndicator('disconnected');
            this.resetSession();
            
            // Attempt to reconnect after 3 seconds
            setTimeout(() => {
                if (!this.isConnected) {
                    this.connect();
                }
            }, 3000);
        };

        this.ws.onerror = (error) => {
            console.error('❌ WebSocket error:', error);
            this.updateStatus('error', 'Connection Error');
            this.updateConnectionIndicator('disconnected');
        };
    }

    handleMessage(data) {
        const now = Date.now();
        
        switch (data.type) {
            case 'session-created':
                console.log('✅ Session created:', data.sessionId);
                this.sessionId = data.sessionId;
                this.updateSessionInfo();
                this.joinSession();
                break;
                
            case 'session-ready':
                console.log('✅ Session ready:', data.sessionId);
                this.sessionId = data.sessionId;
                this.updateSessionInfo();
                this.disconnectBtn.disabled = false;
                break;
                
            case 'dom-update':
                this.handleDOMUpdate(data);
                this.stats.updateCount++;
                this.stats.latency = now - (data.timestamp || now);
                break;

            case 'hybrid-update':
                this.handleHybridUpdate(data);
                this.stats.updateCount++;
                this.stats.latency = now - (data.data.timestamp || now);
                break;
                
            case 'navigation-complete':
                console.log('🌐 Navigation complete:', data.url);
                this.currentUrlEl.textContent = data.url;
                break;
                
            case 'session-error':
                console.error('❌ Session error:', data.error);
                this.updateStatus('error', `Error: ${data.error}`);
                break;
                
            case 'error':
                console.error('❌ Server error:', data.error);
                break;
        }
    }

    handleDOMUpdate(data) {
        if (!data.data || !data.data.html) return;

        try {
            // Create a blob URL for the HTML content
            const htmlContent = this.processHTML(data.data.html);
            const blob = new Blob([htmlContent], { type: 'text/html' });
            const url = URL.createObjectURL(blob);

            // Update iframe with new content
            this.streamFrame.src = url;

            // Show the stream
            this.showStream();

            // Clean up previous blob URL
            setTimeout(() => URL.revokeObjectURL(url), 1000);

        } catch (error) {
            console.error('❌ Failed to update DOM:', error);
        }
    }

    handleHybridUpdate(data) {
        if (!data.data) return;

        try {
            // Use screenshot for visual display (no flickering)
            if (data.data.screenshot) {
                const imgSrc = `data:image/jpeg;base64,${data.data.screenshot}`;

                // Create or update the screenshot display
                let screenshotImg = document.getElementById('screenshotDisplay');
                if (!screenshotImg) {
                    screenshotImg = document.createElement('img');
                    screenshotImg.id = 'screenshotDisplay';
                    screenshotImg.style.width = '100%';
                    screenshotImg.style.height = '100%';
                    screenshotImg.style.objectFit = 'contain';
                    screenshotImg.style.cursor = 'crosshair';
                    screenshotImg.style.display = 'block';

                    // Add click handler for screenshots
                    screenshotImg.addEventListener('click', (e) => this.handleScreenshotClick(e));
                    screenshotImg.addEventListener('mousemove', (e) => this.handleScreenshotMouseMove(e));
                    screenshotImg.addEventListener('keydown', (e) => this.handleScreenshotKeyboard(e));
                    screenshotImg.addEventListener('keyup', (e) => this.handleScreenshotKeyboard(e));

                    // Make it focusable for keyboard events
                    screenshotImg.tabIndex = 0;

                    // Replace iframe with screenshot
                    const container = this.streamFrame.parentNode;
                    container.appendChild(screenshotImg);
                    this.streamFrame.style.display = 'none';
                }

                screenshotImg.src = imgSrc;
                this.showStream();

                // Store DOM for interaction mapping (invisible)
                this.currentDOM = data.data.html;
                this.currentUrl = data.data.url;
                this.currentUrlEl.textContent = data.data.url;
            }

        } catch (error) {
            console.error('❌ Failed to handle hybrid update:', error);
        }
    }

    handleScreenshotClick(event) {
        if (!this.sessionId) return;

        const rect = event.target.getBoundingClientRect();
        const x = Math.round((event.clientX - rect.left) * (1920 / rect.width));
        const y = Math.round((event.clientY - rect.top) * (1080 / rect.height));

        console.log(`🖱️ Screenshot click at (${x}, ${y})`);

        this.sendMouseEvent({
            type: 'click',
            x: x,
            y: y,
            button: event.button === 0 ? 'left' : event.button === 2 ? 'right' : 'middle'
        });

        // Visual feedback
        this.showClickFeedback(event.clientX, event.clientY);

        // Focus for keyboard events
        event.target.focus();
    }

    handleScreenshotMouseMove(event) {
        if (!this.sessionId) return;

        const rect = event.target.getBoundingClientRect();
        const x = Math.round((event.clientX - rect.left) * (1920 / rect.width));
        const y = Math.round((event.clientY - rect.top) * (1080 / rect.height));

        // Throttle mouse move events
        if (!this.lastMouseMove || Date.now() - this.lastMouseMove > 50) {
            this.sendMouseEvent({
                type: 'move',
                x: x,
                y: y
            });
            this.lastMouseMove = Date.now();
        }
    }

    handleScreenshotKeyboard(event) {
        if (!this.sessionId) return;

        // Prevent default browser shortcuts
        if (event.ctrlKey || event.altKey || event.metaKey) {
            event.preventDefault();
        }

        this.sendKeyboardEvent({
            type: event.type,
            key: event.key,
            code: event.code,
            ctrlKey: event.ctrlKey,
            altKey: event.altKey,
            shiftKey: event.shiftKey,
            metaKey: event.metaKey
        });
    }

    showClickFeedback(x, y) {
        const feedback = document.createElement('div');
        feedback.style.position = 'absolute';
        feedback.style.left = x + 'px';
        feedback.style.top = y + 'px';
        feedback.style.width = '20px';
        feedback.style.height = '20px';
        feedback.style.borderRadius = '50%';
        feedback.style.background = 'rgba(0, 255, 136, 0.8)';
        feedback.style.pointerEvents = 'none';
        feedback.style.transform = 'translate(-50%, -50%)';
        feedback.style.animation = 'clickFeedback 0.3s ease-out';
        feedback.style.zIndex = '1000';

        document.body.appendChild(feedback);

        setTimeout(() => {
            document.body.removeChild(feedback);
        }, 300);
    }

    processHTML(html) {
        // Inject event forwarding script into the HTML
        const eventScript = `
            <script>
                // Forward all events to parent window
                function forwardEvent(event) {
                    if (window.parent && window.parent.chromeClient) {
                        const rect = event.target.getBoundingClientRect();
                        const x = event.clientX || (rect.left + rect.width / 2);
                        const y = event.clientY || (rect.top + rect.height / 2);
                        
                        if (event.type === 'click' || event.type === 'mousedown' || event.type === 'mouseup') {
                            window.parent.chromeClient.sendMouseEvent({
                                type: event.type === 'click' ? 'click' : event.type.replace('mouse', ''),
                                x: x,
                                y: y,
                                button: event.button === 0 ? 'left' : event.button === 2 ? 'right' : 'middle'
                            });
                        } else if (event.type === 'mousemove') {
                            window.parent.chromeClient.sendMouseEvent({
                                type: 'move',
                                x: x,
                                y: y
                            });
                        } else if (event.type === 'keydown' || event.type === 'keyup') {
                            window.parent.chromeClient.sendKeyboardEvent({
                                type: event.type,
                                key: event.key,
                                code: event.code,
                                ctrlKey: event.ctrlKey,
                                altKey: event.altKey,
                                shiftKey: event.shiftKey,
                                metaKey: event.metaKey
                            });
                        }
                        
                        // Prevent default to avoid double handling
                        event.preventDefault();
                        event.stopPropagation();
                    }
                }
                
                // Add event listeners when DOM is ready
                document.addEventListener('DOMContentLoaded', function() {
                    ['click', 'mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup'].forEach(eventType => {
                        document.addEventListener(eventType, forwardEvent, true);
                    });
                });
                
                // Also add immediately in case DOM is already loaded
                ['click', 'mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup'].forEach(eventType => {
                    document.addEventListener(eventType, forwardEvent, true);
                });
            </script>
        `;
        
        // Insert the script before closing body tag
        return html.replace('</body>', eventScript + '</body>');
    }

    setupIframeEventForwarding() {
        // This method is called from the injected script
        window.chromeClient = this;
    }

    sendMouseEvent(event) {
        if (!this.sessionId || !this.ws) return;
        
        this.ws.send(JSON.stringify({
            type: 'mouse-event',
            sessionId: this.sessionId,
            ...event
        }));
    }

    sendKeyboardEvent(event) {
        if (!this.sessionId || !this.ws) return;
        
        this.ws.send(JSON.stringify({
            type: 'keyboard-event',
            sessionId: this.sessionId,
            ...event
        }));
    }

    createSession() {
        if (!this.isConnected) return;
        
        console.log('🚀 Creating new Chrome session...');
        this.updateStatus('connecting', 'Creating session...');
        this.createSessionBtn.disabled = true;
        
        this.ws.send(JSON.stringify({
            type: 'create-session'
        }));
    }

    joinSession() {
        if (!this.sessionId || !this.ws) return;
        
        this.ws.send(JSON.stringify({
            type: 'join-session',
            sessionId: this.sessionId
        }));
    }

    navigate(url) {
        if (!this.sessionId || !this.ws) return;
        
        console.log(`🌐 Navigating to: ${url}`);
        this.ws.send(JSON.stringify({
            type: 'navigate',
            sessionId: this.sessionId,
            url: url
        }));
    }

    showStream() {
        this.loadingEl.style.display = 'none';
        this.streamFrame.style.display = 'block';
        this.overlay.style.display = 'block';
        this.updateStatus('connected', 'Streaming');
    }

    startPerformanceMonitoring() {
        setInterval(() => {
            this.updatePerformanceStats();
        }, 1000);
    }

    updatePerformanceStats() {
        const now = Date.now();
        const timeDiff = now - this.stats.lastStatsUpdate;
        
        if (timeDiff >= 1000) {
            this.stats.updatesPerSec = Math.round((this.stats.updateCount * 1000) / timeDiff);
            this.stats.updateCount = 0;
            this.stats.lastStatsUpdate = now;
        }
        
        this.updatesPerSecEl.textContent = this.stats.updatesPerSec;
        this.latencyEl.textContent = this.stats.latency + 'ms';
        this.connectionStatusEl.textContent = this.isConnected ? 'WebSocket' : 'Disconnected';
    }

    updateStatus(type, message) {
        this.statusEl.className = `status ${type}`;
        this.statusEl.textContent = message;
    }

    updateConnectionIndicator(status) {
        this.connectionIndicator.className = `connection-indicator ${status}`;
    }

    updateSessionInfo() {
        this.sessionIdEl.textContent = this.sessionId || 'None';
        this.sessionStatusEl.textContent = this.sessionId ? 'Active' : 'Disconnected';
    }

    resetSession() {
        this.sessionId = null;
        this.loadingEl.style.display = 'block';
        this.streamFrame.style.display = 'none';
        this.overlay.style.display = 'none';
        this.createSessionBtn.disabled = false;
        this.disconnectBtn.disabled = true;
        this.updateSessionInfo();
        this.currentUrlEl.textContent = '-';
    }

    disconnect() {
        if (this.ws) {
            console.log('🔌 Disconnecting...');
            this.ws.close();
            this.resetSession();
        }
    }
}

// Helper functions
function navigateTo(url) {
    if (window.chromeClient && window.chromeClient.sessionId) {
        window.chromeClient.navigate(url);
    } else {
        alert('Please create a session first');
    }
}

function pasteText() {
    const text = document.getElementById('pasteText').value;
    if (!text) {
        alert('Please enter text to paste');
        return;
    }
    
    if (window.chromeClient && window.chromeClient.sessionId) {
        console.log(`📋 Pasting text: ${text}`);
        window.chromeClient.sendKeyboardEvent({
            type: 'paste',
            text: text
        });
        
        document.getElementById('pasteText').value = '';
    } else {
        alert('Please create a session first');
    }
}

function pastePassword() {
    const password = prompt('Enter password to paste:');
    if (password) {
        if (window.chromeClient && window.chromeClient.sessionId) {
            console.log(`🔑 Pasting password (${password.length} chars)`);
            window.chromeClient.sendKeyboardEvent({
                type: 'paste',
                text: password
            });
        } else {
            alert('Please create a session first');
        }
    }
}

function sendCtrlV() {
    if (window.chromeClient && window.chromeClient.sessionId) {
        console.log(`⌨️ Sending Ctrl+V`);
        
        window.chromeClient.sendKeyboardEvent({
            type: 'keydown',
            key: 'Control',
            ctrlKey: true
        });
        
        window.chromeClient.sendKeyboardEvent({
            type: 'keydown',
            key: 'v',
            ctrlKey: true
        });
        
        window.chromeClient.sendKeyboardEvent({
            type: 'keyup',
            key: 'v'
        });
        
        window.chromeClient.sendKeyboardEvent({
            type: 'keyup',
            key: 'Control'
        });
    } else {
        alert('Please create a session first');
    }
}

// Initialize client when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.chromeClient = new WebSocketChromeClient();
});
