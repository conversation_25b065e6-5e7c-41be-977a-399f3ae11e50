{"version": 3, "file": "install.js", "sourceRoot": "", "sources": ["../../src/install.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAC,SAAS,EAAC,MAAM,eAAe,CAAC;AACxC,OAAO,EAAC,UAAU,EAAC,MAAM,IAAI,CAAC;AAC9B,OAAO,EAAC,KAAK,EAAE,MAAM,EAAC,MAAM,aAAa,CAAC;AAC1C,OAAO,EAAE,MAAM,IAAI,CAAC;AACpB,OAAO,IAAI,MAAM,MAAM,CAAC;AAExB,OAAO,EACL,OAAO,EACP,eAAe,EACf,YAAY,GACb,MAAM,gCAAgC,CAAC;AACxC,OAAO,EAAC,KAAK,EAAE,gBAAgB,EAAC,MAAM,YAAY,CAAC;AACnD,OAAO,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AACjC,OAAO,EAAC,qBAAqB,EAAC,MAAM,qBAAqB,CAAC;AAC1D,OAAO,EAAC,aAAa,EAAC,MAAM,eAAe,CAAC;AAC5C,OAAO,EAAC,YAAY,EAAE,OAAO,EAAE,eAAe,EAAC,MAAM,eAAe,CAAC;AAErE,MAAM,YAAY,GAAG,KAAK,CAAC,4BAA4B,CAAC,CAAC;AAEzD,MAAM,KAAK,GAAG,IAAI,GAAG,EAA4B,CAAC;AAClD,SAAS,SAAS,CAAC,KAAa;IAC9B,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;AACrC,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IACjC,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAC7B,MAAM,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,OAAO;IACT,CAAC;IACD,MAAM,QAAQ,GACZ,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,qCAAqC;IAC1G,YAAY,CAAC,gBAAgB,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC;AACvD,CAAC;AA0ED,MAAM,CAAC,KAAK,UAAU,OAAO,CAC3B,OAAuB;IAEvB,OAAO,CAAC,QAAQ,KAAK,qBAAqB,EAAE,CAAC;IAC7C,OAAO,CAAC,MAAM,KAAK,IAAI,CAAC;IACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,MAAM,GAAG,GAAG,cAAc,CACxB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CAAC;IACF,IAAI,CAAC;QACH,OAAO,MAAM,UAAU,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,oEAAoE;QACpE,IAAI,OAAO,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,uBAAuB,EAAE,CAAC;YACxD,MAAM,GAAG,CAAC;QACZ,CAAC;QACD,YAAY,CAAC,0BAA0B,GAAG,GAAG,CAAC,CAAC;QAC/C,QAAQ,OAAO,CAAC,OAAO,EAAE,CAAC;YACxB,KAAK,OAAO,CAAC,MAAM,CAAC;YACpB,KAAK,OAAO,CAAC,YAAY,CAAC;YAC1B,KAAK,OAAO,CAAC,mBAAmB,CAAC,CAAC,CAAC;gBACjC,YAAY,CACV,wFAAwF,CACzF,CAAC;gBAIF,MAAM,OAAO,GAAG,CAAC,MAAM,OAAO,CAC5B,IAAI,GAAG,CACL,yDAAyD,OAAO,CAAC,OAAO,OAAO,CAChF,CACF,CAAY,CAAC;gBACd,IAAI,QAAQ,GAAG,EAAE,CAAC;gBAClB,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzB,KAAK,eAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,SAAS,CAAC;wBACrB,MAAM;oBACR,KAAK,eAAe,CAAC,OAAO;wBAC1B,QAAQ,GAAG,WAAW,CAAC;wBACvB,MAAM;oBACR,KAAK,eAAe,CAAC,GAAG;wBACtB,QAAQ,GAAG,SAAS,CAAC;wBACrB,MAAM;oBACR,KAAK,eAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,OAAO,CAAC;wBACnB,MAAM;oBACR,KAAK,eAAe,CAAC,KAAK;wBACxB,QAAQ,GAAG,OAAO,CAAC;wBACnB,MAAM;gBACV,CAAC;gBACD,MAAM,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC1D,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,QAAQ,CAAC;gBACvC,CAAC,CAAC,EAAE,GAAG,CAAC;gBACR,IAAI,GAAG,EAAE,CAAC;oBACR,YAAY,CAAC,oCAAoC,GAAG,GAAG,CAAC,CAAC;oBACzD,OAAO,MAAM,UAAU,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;gBACjD,CAAC;gBACD,MAAM,GAAG,CAAC;YACZ,CAAC;YACD;gBACE,MAAM,GAAG,CAAC;QACd,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,UAAU,CACvB,GAAQ,EACR,OAAuB;IAEvB,OAAO,CAAC,QAAQ,KAAK,qBAAqB,EAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IACjD,MAAM,CAAC,QAAQ,EAAE,uCAAuC,GAAG,GAAG,CAAC,CAAC;IAChE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC1C,MAAM,WAAW,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IACvD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,CAAC,OAAO,IAAI,QAAQ,EAAE,CAAC,CAAC;IAC7E,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;QAC7B,MAAM,KAAK,CAAC,WAAW,EAAE,EAAC,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;IAC9C,CAAC;IAED,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;QACpB,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,SAAS,CAAC,UAAU,CAAC,CAAC;QACtB,MAAM,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACvE,YAAY,CAAC,UAAU,CAAC,CAAC;QACzB,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,MAAM,UAAU,GAAG,KAAK,CAAC,eAAe,CACtC,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;IAEF,IAAI,CAAC;QACH,IAAI,UAAU,CAAC,UAAU,CAAC,EAAE,CAAC;YAC3B,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAC3C,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,CACjB,CAAC;YACF,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjD,MAAM,IAAI,KAAK,CACb,uBAAuB,UAAU,gCAAgC,gBAAgB,CAAC,cAAc,cAAc,CAC/G,CAAC;YACJ,CAAC;YACD,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC;YACjC,OAAO,gBAAgB,CAAC;QAC1B,CAAC;QACD,YAAY,CAAC,2BAA2B,GAAG,EAAE,CAAC,CAAC;QAC/C,IAAI,CAAC;YACH,SAAS,CAAC,UAAU,CAAC,CAAC;YACtB,MAAM,YAAY,CAAC,GAAG,EAAE,WAAW,EAAE,OAAO,CAAC,wBAAwB,CAAC,CAAC;QACzE,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,UAAU,CAAC,CAAC;QAC3B,CAAC;QAED,YAAY,CAAC,cAAc,WAAW,OAAO,UAAU,EAAE,CAAC,CAAC;QAC3D,IAAI,CAAC;YACH,SAAS,CAAC,SAAS,CAAC,CAAC;YACrB,MAAM,aAAa,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QAC/C,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,SAAS,CAAC,CAAC;QAC1B,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAI,gBAAgB,CAC3C,KAAK,EACL,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,CACjB,CAAC;QACF,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,YAAY,EAAE,CAAC;YACjD,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;YACzD,gBAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAC3C,CAAC;QAED,MAAM,QAAQ,CAAC,gBAAgB,CAAC,CAAC;QACjC,OAAO,gBAAgB,CAAC;IAC1B,CAAC;YAAS,CAAC;QACT,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE,CAAC;YAC5B,MAAM,MAAM,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC;IACH,CAAC;AACH,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,gBAAkC;IACxD,iEAAiE;IACjE,IACE,CAAC,gBAAgB,CAAC,QAAQ,KAAK,eAAe,CAAC,KAAK;QAClD,gBAAgB,CAAC,QAAQ,KAAK,eAAe,CAAC,KAAK,CAAC;QACtD,gBAAgB,CAAC,OAAO,KAAK,OAAO,CAAC,MAAM;QAC3C,gBAAgB,CAAC,QAAQ,KAAK,qBAAqB,EAAE,EACrD,CAAC;QACD,IAAI,CAAC;YACH,SAAS,CAAC,aAAa,CAAC,CAAC;YACzB,MAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;YACjE,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YACxD,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC9B,OAAO;YACT,CAAC;YACD,SAAS,CACP,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,EAClC,CAAC,mCAAmC,GAAG,UAAU,CAAC,EAClD;gBACE,KAAK,EAAE,IAAI;aACZ,CACF,CAAC;YACF,6DAA6D;YAC7D,qDAAqD;QACvD,CAAC;gBAAS,CAAC;YACT,YAAY,CAAC,aAAa,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC;AACH,CAAC;AA0BD;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,SAAS,CAAC,OAAyB;IACvD,OAAO,CAAC,QAAQ,KAAK,qBAAqB,EAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,2CAA2C,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CAC1E,CAAC;IACJ,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,SAAS,CACnC,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,CAChB,CAAC;AACJ,CAAC;AAYD;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB,CACxC,OAAoC;IAEpC,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,oBAAoB,EAAE,CAAC;AAC5D,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,OAAuB;IACvD,OAAO,CAAC,QAAQ,KAAK,qBAAqB,EAAE,CAAC;IAC7C,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;QACtB,MAAM,IAAI,KAAK,CACb,uDAAuD,EAAE,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,IAAI,EAAE,GAAG,CACtF,CAAC;IACJ,CAAC;IACD,OAAO,MAAM,eAAe,CAC1B,cAAc,CACZ,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,OAAO,CAChB,CACF,CAAC;AACJ,CAAC;AAED,SAAS,cAAc,CACrB,OAAgB,EAChB,QAAyB,EACzB,OAAe,EACf,OAAgB;IAEhB,OAAO,IAAI,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AACpE,CAAC"}